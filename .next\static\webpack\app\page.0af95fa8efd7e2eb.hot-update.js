"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: function() { return /* binding */ AuthService; }\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _services_adminSettings__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./services/adminSettings */ \"(app-pages-browser)/./src/lib/services/adminSettings.ts\");\n/* harmony import */ var _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./services/referralSystem */ \"(app-pages-browser)/./src/lib/services/referralSystem.ts\");\n\n\n\nclass AuthService {\n    /**\n   * Sign up a new user with OTP verification and referral processing\n   */ static async signUp(email, password, userData) {\n        // Check if email verification is required (with fallback to false if table doesn't exist)\n        let requireEmailVerification = false;\n        try {\n            requireEmailVerification = await _services_adminSettings__WEBPACK_IMPORTED_MODULE_1__.AdminSettingsService.getSetting(\"require_email_verification\") || false;\n        } catch (error) {\n            // Gracefully handle admin settings access failures\n            console.warn(\"Could not fetch email verification setting, defaulting to false:\", error);\n            requireEmailVerification = false;\n            // If it's a 406 error or RLS policy error, it means the table exists but access is restricted\n            // This is expected during signup when user is not authenticated yet\n            if (error instanceof Error && (error.message.includes(\"406\") || error.message.includes(\"policy\"))) {\n                console.info(\"Admin settings access restricted during signup - using default values\");\n            }\n        }\n        // Validate referral code if provided\n        let referrer = null;\n        if (userData === null || userData === void 0 ? void 0 : userData.referralCode) {\n            try {\n                referrer = await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.validateReferralCode(userData.referralCode);\n                if (!referrer) {\n                    throw new Error(\"Invalid referral code\");\n                }\n            } catch (error) {\n                console.warn(\"Could not validate referral code:\", error);\n            // Don't fail signup for referral code issues, just log the warning\n            }\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    ...userData,\n                    referral_code: userData === null || userData === void 0 ? void 0 : userData.referralCode,\n                    full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n                    phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n                    location: userData === null || userData === void 0 ? void 0 : userData.location,\n                    referrer_id: referrer === null || referrer === void 0 ? void 0 : referrer.id\n                }\n            }\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        // Always create user profile immediately (regardless of email verification)\n        if (data.user) {\n            try {\n                // Create basic profile first without complex operations\n                await this.createBasicUserProfile(data.user, userData);\n                // Defer complex operations like referral hierarchy placement\n                if (referrer) {\n                    // Schedule referral placement for later (don't block signup)\n                    this.scheduleReferralPlacement(data.user.id, referrer.id).catch((error)=>{\n                        console.error(\"Failed to schedule referral placement:\", error);\n                    });\n                }\n            } catch (profileError) {\n                console.error(\"Error creating user profile:\", profileError);\n            // Don't fail the signup, but log the error\n            }\n        }\n        return {\n            data,\n            requireEmailVerification,\n            referrer\n        };\n    }\n    /**\n   * Create basic user profile without complex operations (fast)\n   * Uses regular supabase client with RLS policies that allow user creation\n   */ static async createBasicUserProfile(authUser, userData) {\n        console.log(\"Creating basic user profile for:\", authUser.email);\n        // Use regular supabase client - RLS policies should allow user to create their own profile\n        const { error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").insert({\n            id: authUser.id,\n            email: authUser.email,\n            full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n            phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n            location: userData === null || userData === void 0 ? void 0 : userData.location,\n            user_type: \"user\",\n            role: \"user\",\n            is_super_admin: false\n        });\n        if (profileError) {\n            console.error(\"Profile creation error:\", profileError);\n            throw new Error(\"Failed to create user profile: \".concat(profileError.message));\n        }\n        console.log(\"✅ Basic user profile created successfully\");\n    }\n    /**\n   * Schedule referral placement for later processing (non-blocking)\n   */ static async scheduleReferralPlacement(userId, referrerId) {\n        try {\n            // Use a timeout to defer this operation\n            setTimeout(async ()=>{\n                try {\n                    await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.placeUserInHierarchy(userId, referrerId);\n                    console.log(\"Referral placement completed for user:\", userId);\n                } catch (error) {\n                    console.error(\"Deferred referral placement failed:\", error);\n                }\n            }, 2000) // 2 second delay\n            ;\n        } catch (error) {\n            console.error(\"Failed to schedule referral placement:\", error);\n        }\n    }\n    /**\n   * Create user profile in public.users table (legacy method with full operations)\n   */ static async createUserProfile(authUser, userData, referrer) {\n        // Check if admin client is available\n        if (!_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin) {\n            throw new Error(\"Admin client not available - missing SUPABASE_SERVICE_ROLE_KEY\");\n        }\n        // Use service role client to bypass RLS for user creation\n        const { error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"users\").insert({\n            id: authUser.id,\n            email: authUser.email,\n            full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n            phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n            location: userData === null || userData === void 0 ? void 0 : userData.location,\n            user_type: \"user\",\n            referral_level: referrer ? (referrer.referral_level || 0) + 1 : 0,\n            referred_by_id: (referrer === null || referrer === void 0 ? void 0 : referrer.id) || null\n        });\n        if (profileError) {\n            throw new Error(\"Failed to create user profile: \".concat(profileError.message));\n        }\n        // If user has a referrer, place them in hierarchy\n        if (referrer) {\n            try {\n                await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.placeUserInHierarchy(authUser.id, referrer.id);\n            } catch (referralError) {\n                console.error(\"Failed to place user in referral hierarchy:\", referralError);\n            // Don't fail the profile creation, but log the error\n            }\n        }\n    }\n    /**\n   * Verify email OTP and process referral placement\n   */ static async verifyEmailOtp(email, token) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.verifyOtp({\n            email,\n            token,\n            type: \"signup\"\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        // After successful verification, create user profile if it doesn't exist\n        if (data.user) {\n            try {\n                // Check if user profile already exists\n                const { data: existingUser } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"id\").eq(\"id\", data.user.id).single();\n                if (!existingUser) {\n                    var _data_user_user_metadata, _data_user_user_metadata1, _data_user_user_metadata2, _data_user_user_metadata3, _data_user_user_metadata4;\n                    // Get referrer if referral code exists\n                    let referrer = null;\n                    if ((_data_user_user_metadata = data.user.user_metadata) === null || _data_user_user_metadata === void 0 ? void 0 : _data_user_user_metadata.referral_code) {\n                        referrer = await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.validateReferralCode(data.user.user_metadata.referral_code);\n                    }\n                    // Create user profile\n                    await this.createUserProfile(data.user, {\n                        full_name: (_data_user_user_metadata1 = data.user.user_metadata) === null || _data_user_user_metadata1 === void 0 ? void 0 : _data_user_user_metadata1.full_name,\n                        phone: (_data_user_user_metadata2 = data.user.user_metadata) === null || _data_user_user_metadata2 === void 0 ? void 0 : _data_user_user_metadata2.phone,\n                        location: (_data_user_user_metadata3 = data.user.user_metadata) === null || _data_user_user_metadata3 === void 0 ? void 0 : _data_user_user_metadata3.location,\n                        referralCode: (_data_user_user_metadata4 = data.user.user_metadata) === null || _data_user_user_metadata4 === void 0 ? void 0 : _data_user_user_metadata4.referral_code\n                    }, referrer);\n                }\n            } catch (profileError) {\n                console.error(\"Error creating user profile after verification:\", profileError);\n            // Don't fail the verification, but log the error\n            }\n        }\n        return data;\n    }\n    /**\n   * Resend email OTP\n   */ static async resendEmailOtp(email) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.resend({\n            type: \"signup\",\n            email\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Sign in with email and password\n   */ static async signIn(email, password) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Sign out the current user\n   */ static async signOut() {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut();\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Get the current user session\n   */ static async getSession() {\n        const { data: { session }, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return session;\n    }\n    /**\n   * Get the current user with improved error handling\n   */ static async getCurrentUser() {\n        try {\n            // First check if we have a valid session\n            const { data: { session }, error: sessionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (sessionError) {\n                console.error(\"Session error:\", sessionError);\n                return null;\n            }\n            if (!(session === null || session === void 0 ? void 0 : session.user)) {\n                console.log(\"No active session found\");\n                return null;\n            }\n            const user = session.user;\n            console.log(\"Found active session for user:\", user.email);\n            // Get additional user data from users table with retry logic\n            let retryCount = 0;\n            const maxRetries = 3;\n            while(retryCount < maxRetries){\n                try {\n                    const { data: profile, error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"*\").eq(\"id\", user.id).single();\n                    if (profileError) {\n                        if (profileError.code === \"PGRST116\") {\n                            var _user_user_metadata, _user_user_metadata1, _user_user_metadata2;\n                            // User profile doesn't exist, create it\n                            console.log(\"User profile not found, creating...\");\n                            const newProfile = {\n                                id: user.id,\n                                email: user.email,\n                                full_name: ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || null,\n                                phone: ((_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.phone) || null,\n                                location: ((_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.location) || null,\n                                role: \"user\",\n                                is_super_admin: false\n                            };\n                            const { data: createdProfile, error: createError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").insert(newProfile).select().single();\n                            if (createError) {\n                                console.error(\"Error creating user profile:\", createError);\n                                return {\n                                    id: user.id,\n                                    email: user.email\n                                };\n                            }\n                            return createdProfile;\n                        } else {\n                            throw profileError;\n                        }\n                    }\n                    return profile;\n                } catch (error) {\n                    retryCount++;\n                    console.error(\"Error fetching user profile (attempt \".concat(retryCount, \"):\"), error);\n                    if (retryCount >= maxRetries) {\n                        console.error(\"Max retries reached, returning basic user info\");\n                        return {\n                            id: user.id,\n                            email: user.email\n                        };\n                    }\n                    // Wait before retry\n                    await new Promise((resolve)=>setTimeout(resolve, 1000 * retryCount));\n                }\n            }\n            return null;\n        } catch (error) {\n            console.error(\"Error in getCurrentUser:\", error);\n            return null;\n        }\n    }\n    /**\n   * Update user profile\n   */ static async updateProfile(userId, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").update(updates).eq(\"id\", userId).select().single();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Reset password\n   */ static async resetPassword(email) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Update password\n   */ static async updatePassword(newPassword) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.updateUser({\n            password: newPassword\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Listen to auth state changes with improved handling\n   */ static onAuthStateChange(callback) {\n        return _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            var _session_user;\n            console.log(\"Auth state change detected:\", event, session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.email);\n            // Add a small delay to ensure state consistency\n            setTimeout(()=>{\n                callback(event, session);\n            }, 100);\n        });\n    }\n    /**\n   * Check if current session is valid\n   */ static async isSessionValid() {\n        try {\n            const { data: { session }, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (error || !session) {\n                return false;\n            }\n            // Check if token is expired\n            const now = Math.floor(Date.now() / 1000);\n            if (session.expires_at && session.expires_at < now) {\n                console.log(\"Session token expired\");\n                return false;\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Error checking session validity:\", error);\n            return false;\n        }\n    }\n    /**\n   * Refresh session if needed\n   */ static async refreshSession() {\n        try {\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.refreshSession();\n            if (error) {\n                console.error(\"Error refreshing session:\", error);\n                return false;\n            }\n            return !!data.session;\n        } catch (error) {\n            console.error(\"Error in refreshSession:\", error);\n            return false;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/auth.ts\n"));

/***/ })

});