// Test script to verify admin settings update works correctly
const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function testAdminSettingsUpdate() {
  console.log('🧪 Testing admin settings update...\n')
  
  try {
    // Test 1: Read current settings
    console.log('1. Reading current admin settings...')
    const { data: currentSettings, error: readError } = await supabase
      .from('admin_settings')
      .select('key, value')
    
    if (readError) {
      console.error('❌ Error reading settings:', readError.message)
      return false
    }
    
    console.log('✅ Current settings:', currentSettings.length, 'records found')
    
    // Test 2: Update a single setting using the new method
    console.log('\n2. Testing single setting update...')
    const testValue = `Test Site Name ${Date.now()}`
    
    const { error: updateError } = await supabase
      .from('admin_settings')
      .upsert({
        key: 'site_name',
        value: testValue,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'key',
        ignoreDuplicates: false
      })
    
    if (updateError) {
      console.error('❌ Error updating single setting:', updateError.message)
      return false
    }
    
    console.log('✅ Single setting updated successfully')
    
    // Test 3: Verify the update
    console.log('\n3. Verifying the update...')
    const { data: updatedSetting, error: verifyError } = await supabase
      .from('admin_settings')
      .select('value')
      .eq('key', 'site_name')
      .single()
    
    if (verifyError) {
      console.error('❌ Error verifying update:', verifyError.message)
      return false
    }
    
    if (updatedSetting.value === testValue) {
      console.log('✅ Update verified successfully')
    } else {
      console.error('❌ Update verification failed. Expected:', testValue, 'Got:', updatedSetting.value)
      return false
    }
    
    // Test 4: Batch update multiple settings
    console.log('\n4. Testing batch update...')
    const batchUpdates = [
      { key: 'site_name', value: 'OKDOI', updated_at: new Date().toISOString() },
      { key: 'site_description', value: 'Premium Marketplace for Everything', updated_at: new Date().toISOString() },
      { key: 'admin_email', value: '<EMAIL>', updated_at: new Date().toISOString() }
    ]
    
    const { error: batchError } = await supabase
      .from('admin_settings')
      .upsert(batchUpdates, {
        onConflict: 'key',
        ignoreDuplicates: false
      })
    
    if (batchError) {
      console.error('❌ Error in batch update:', batchError.message)
      return false
    }
    
    console.log('✅ Batch update completed successfully')
    
    // Test 5: Verify batch update
    console.log('\n5. Verifying batch update...')
    const { data: batchVerify, error: batchVerifyError } = await supabase
      .from('admin_settings')
      .select('key, value')
      .in('key', ['site_name', 'site_description', 'admin_email'])
    
    if (batchVerifyError) {
      console.error('❌ Error verifying batch update:', batchVerifyError.message)
      return false
    }
    
    console.log('✅ Batch update verified:', batchVerify.length, 'settings updated')
    
    return true
    
  } catch (error) {
    console.error('❌ Exception during testing:', error.message)
    return false
  }
}

async function testDuplicateKeyHandling() {
  console.log('\n🧪 Testing duplicate key handling...\n')
  
  try {
    // Try to insert a duplicate key to see if upsert handles it correctly
    const { error } = await supabase
      .from('admin_settings')
      .upsert({
        key: 'site_name',
        value: 'OKDOI Updated',
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'key',
        ignoreDuplicates: false
      })
    
    if (error) {
      console.error('❌ Duplicate key handling failed:', error.message)
      return false
    }
    
    console.log('✅ Duplicate key handled correctly with upsert')
    return true
    
  } catch (error) {
    console.error('❌ Exception during duplicate key test:', error.message)
    return false
  }
}

async function main() {
  console.log('🔧 Testing OKDOI Admin Settings Update Fixes...\n')
  
  const updateTest = await testAdminSettingsUpdate()
  const duplicateTest = await testDuplicateKeyHandling()
  
  console.log('\n📊 Test Results:')
  console.log('- Admin Settings Update:', updateTest ? '✅ PASSED' : '❌ FAILED')
  console.log('- Duplicate Key Handling:', duplicateTest ? '✅ PASSED' : '❌ FAILED')
  
  if (updateTest && duplicateTest) {
    console.log('\n🎉 All tests passed! Admin settings should work now.')
  } else {
    console.log('\n❌ Some tests failed. Check the errors above.')
  }
}

main().catch(console.error)
