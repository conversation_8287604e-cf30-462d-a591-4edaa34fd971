const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')

require('dotenv').config({ path: '.env.local' })

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL, 
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

async function applyFix() {
  try {
    console.log('Applying INSERT policy fix...')
    
    const sql = fs.readFileSync('fix-insert-policy.sql', 'utf8')
    console.log('SQL to execute:', sql)
    
    const { data, error } = await supabaseAdmin.rpc('exec_sql', {
      query: sql
    })
    
    if (error) {
      console.error('❌ Error applying fix:', error.message)
      return false
    }
    
    console.log('✅ INSERT policy created successfully')
    return true
    
  } catch (error) {
    console.error('❌ Exception:', error.message)
    return false
  }
}

applyFix().then(success => {
  console.log('Result:', success ? '✅ SUCCESS' : '❌ FAILED')
})
