{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "cQ30YHWiyKd3LAH8qaeYHm7pMG3IwjKZrf1fCLg4Zo4="}}}, "functions": {}, "sortedMiddleware": ["/"]}