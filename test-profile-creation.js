// Test profile creation directly
const { createClient } = require('@supabase/supabase-js')

require('dotenv').config({ path: '.env.local' })

const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY)
const supabaseAdmin = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY)

async function testProfileCreation() {
  console.log('🧪 Testing profile creation...\n')
  
  const testEmail = `profiletest${Date.now()}@gmail.com`
  const testPassword = 'testpassword123'
  
  try {
    // Step 1: Create auth user
    console.log('1. Creating auth user...')
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword
    })
    
    if (authError) {
      console.error('❌ Auth signup error:', authError.message)
      return false
    }
    
    if (!authData.user) {
      console.error('❌ No user data returned from auth')
      return false
    }
    
    console.log('✅ Auth user created:', authData.user.id)
    
    // Step 2: Try to create profile using regular client (should work with RLS)
    console.log('\n2. Testing profile creation with regular client...')
    
    const { error: profileError } = await supabase
      .from('users')
      .insert({
        id: authData.user.id,
        email: authData.user.email,
        full_name: 'Test Profile User',
        phone: '1234567890',
        location: 'Test Location',
        user_type: 'user',
        role: 'user',
        is_super_admin: false
      })
    
    if (profileError) {
      console.error('❌ Profile creation error:', {
        message: profileError.message,
        code: profileError.code,
        details: profileError.details,
        hint: profileError.hint
      })
      
      // Try with admin client as fallback
      console.log('\n3. Trying with admin client...')
      const { error: adminProfileError } = await supabaseAdmin
        .from('users')
        .insert({
          id: authData.user.id,
          email: authData.user.email,
          full_name: 'Test Profile User',
          phone: '1234567890',
          location: 'Test Location',
          user_type: 'user',
          role: 'user',
          is_super_admin: false
        })
      
      if (adminProfileError) {
        console.error('❌ Admin profile creation also failed:', adminProfileError.message)
        return false
      } else {
        console.log('✅ Profile created with admin client')
      }
    } else {
      console.log('✅ Profile created with regular client')
    }
    
    // Step 3: Verify profile was created
    console.log('\n4. Verifying profile creation...')
    const { data: profile, error: fetchError } = await supabase
      .from('users')
      .select('*')
      .eq('id', authData.user.id)
      .single()
    
    if (fetchError) {
      console.error('❌ Failed to fetch created profile:', fetchError.message)
      return false
    }
    
    console.log('✅ Profile verified:', {
      id: profile.id,
      email: profile.email,
      full_name: profile.full_name,
      referral_code: profile.referral_code ? 'Generated' : 'Missing'
    })
    
    // Cleanup
    console.log('\n5. Cleaning up...')
    await supabaseAdmin.auth.admin.deleteUser(authData.user.id)
    console.log('✅ Test user cleaned up')
    
    return true
    
  } catch (error) {
    console.error('❌ Exception during test:', error.message)
    return false
  }
}

testProfileCreation().then(success => {
  console.log('\n📋 Result:', success ? '✅ SUCCESS' : '❌ FAILED')
}).catch(console.error)
