// Test script to verify signup works after fixes
const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing required environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testAdminSettingsAccess() {
  console.log('Testing admin settings access...')
  
  try {
    // Test reading admin settings (this should work now)
    const { data, error } = await supabase
      .from('admin_settings')
      .select('key, value')
      .eq('key', 'require_email_verification')
      .single()
    
    if (error) {
      console.error('❌ Error accessing admin settings:', error.message, error.code)
      return false
    } else {
      console.log('✅ Successfully accessed admin settings:', data)
      return true
    }
  } catch (error) {
    console.error('❌ Exception accessing admin settings:', error.message)
    return false
  }
}

async function testSignupFlow() {
  console.log('Testing signup flow...')
  
  try {
    // Test the signup process with a dummy email
    const testEmail = `testuser${Date.now()}@gmail.com`
    const testPassword = 'testpassword123'
    
    const { data, error } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          full_name: 'Test User',
          phone: '1234567890',
          location: 'Test Location'
        }
      }
    })
    
    if (error) {
      console.error('❌ Signup error:', {
        message: error.message,
        status: error.status,
        code: error.code,
        details: error.details,
        hint: error.hint,
        full: error
      })
      return false
    } else {
      console.log('✅ Signup successful:', {
        email: data.user?.email,
        id: data.user?.id,
        confirmed: data.user?.email_confirmed_at ? 'Yes' : 'No'
      })

      // Don't clean up for now to verify the user was created
      console.log('✅ Test user created successfully')

      return true
    }
  } catch (error) {
    console.error('❌ Exception during signup:', error.message)
    return false
  }
}

async function main() {
  console.log('🧪 Testing OKDOI signup fixes...\n')
  
  const settingsTest = await testAdminSettingsAccess()
  console.log('')
  
  const signupTest = await testSignupFlow()
  console.log('')
  
  if (settingsTest && signupTest) {
    console.log('🎉 All tests passed! Signup should work now.')
  } else {
    console.log('❌ Some tests failed. Check the errors above.')
  }
}

main().catch(console.error)
