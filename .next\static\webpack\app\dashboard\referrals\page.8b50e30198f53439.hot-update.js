"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/referrals/page",{

/***/ "(app-pages-browser)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: function() { return /* binding */ AuthService; }\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _services_adminSettings__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./services/adminSettings */ \"(app-pages-browser)/./src/lib/services/adminSettings.ts\");\n/* harmony import */ var _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./services/referralSystem */ \"(app-pages-browser)/./src/lib/services/referralSystem.ts\");\n\n\n\nclass AuthService {\n    /**\n   * Sign up a new user with OTP verification and referral processing\n   */ static async signUp(email, password, userData) {\n        // Check if email verification is required (with fallback to false if table doesn't exist)\n        let requireEmailVerification = false;\n        try {\n            requireEmailVerification = await _services_adminSettings__WEBPACK_IMPORTED_MODULE_1__.AdminSettingsService.getSetting(\"require_email_verification\") || false;\n        } catch (error) {\n            // Gracefully handle admin settings access failures\n            console.warn(\"Could not fetch email verification setting, defaulting to false:\", error);\n            requireEmailVerification = false;\n            // If it's a 406 error or RLS policy error, it means the table exists but access is restricted\n            // This is expected during signup when user is not authenticated yet\n            if (error instanceof Error && (error.message.includes(\"406\") || error.message.includes(\"policy\"))) {\n                console.info(\"Admin settings access restricted during signup - using default values\");\n            }\n        }\n        // Validate referral code if provided\n        let referrer = null;\n        if (userData === null || userData === void 0 ? void 0 : userData.referralCode) {\n            try {\n                referrer = await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.validateReferralCode(userData.referralCode);\n                if (!referrer) {\n                    throw new Error(\"Invalid referral code\");\n                }\n            } catch (error) {\n                console.warn(\"Could not validate referral code:\", error);\n            // Don't fail signup for referral code issues, just log the warning\n            }\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    ...userData,\n                    referral_code: userData === null || userData === void 0 ? void 0 : userData.referralCode,\n                    full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n                    phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n                    location: userData === null || userData === void 0 ? void 0 : userData.location,\n                    referrer_id: referrer === null || referrer === void 0 ? void 0 : referrer.id\n                }\n            }\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        // If user is created and email verification is NOT required, create profile immediately\n        if (data.user && !requireEmailVerification) {\n            try {\n                // Create basic profile first without complex operations\n                await this.createBasicUserProfile(data.user, userData);\n                // Defer complex operations like referral hierarchy placement\n                if (referrer) {\n                    // Schedule referral placement for later (don't block signup)\n                    this.scheduleReferralPlacement(data.user.id, referrer.id).catch((error)=>{\n                        console.error(\"Failed to schedule referral placement:\", error);\n                    });\n                }\n            } catch (profileError) {\n                console.error(\"Error creating user profile:\", profileError);\n            // Don't fail the signup, but log the error\n            }\n        }\n        return {\n            data,\n            requireEmailVerification,\n            referrer\n        };\n    }\n    /**\n   * Create user profile in public.users table\n   */ static async createUserProfile(authUser, userData, referrer) {\n        // Check if admin client is available\n        if (!_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin) {\n            throw new Error(\"Admin client not available - missing SUPABASE_SERVICE_ROLE_KEY\");\n        }\n        // Use service role client to bypass RLS for user creation\n        const { error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"users\").insert({\n            id: authUser.id,\n            email: authUser.email,\n            full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n            phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n            location: userData === null || userData === void 0 ? void 0 : userData.location,\n            user_type: \"user\",\n            referral_level: referrer ? (referrer.referral_level || 0) + 1 : 0,\n            referred_by_id: (referrer === null || referrer === void 0 ? void 0 : referrer.id) || null\n        });\n        if (profileError) {\n            throw new Error(\"Failed to create user profile: \".concat(profileError.message));\n        }\n        // If user has a referrer, place them in hierarchy\n        if (referrer) {\n            try {\n                await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.placeUserInHierarchy(authUser.id, referrer.id);\n            } catch (referralError) {\n                console.error(\"Failed to place user in referral hierarchy:\", referralError);\n            // Don't fail the profile creation, but log the error\n            }\n        }\n    }\n    /**\n   * Verify email OTP and process referral placement\n   */ static async verifyEmailOtp(email, token) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.verifyOtp({\n            email,\n            token,\n            type: \"signup\"\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        // After successful verification, create user profile if it doesn't exist\n        if (data.user) {\n            try {\n                // Check if user profile already exists\n                const { data: existingUser } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"id\").eq(\"id\", data.user.id).single();\n                if (!existingUser) {\n                    var _data_user_user_metadata, _data_user_user_metadata1, _data_user_user_metadata2, _data_user_user_metadata3, _data_user_user_metadata4;\n                    // Get referrer if referral code exists\n                    let referrer = null;\n                    if ((_data_user_user_metadata = data.user.user_metadata) === null || _data_user_user_metadata === void 0 ? void 0 : _data_user_user_metadata.referral_code) {\n                        referrer = await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.validateReferralCode(data.user.user_metadata.referral_code);\n                    }\n                    // Create user profile\n                    await this.createUserProfile(data.user, {\n                        full_name: (_data_user_user_metadata1 = data.user.user_metadata) === null || _data_user_user_metadata1 === void 0 ? void 0 : _data_user_user_metadata1.full_name,\n                        phone: (_data_user_user_metadata2 = data.user.user_metadata) === null || _data_user_user_metadata2 === void 0 ? void 0 : _data_user_user_metadata2.phone,\n                        location: (_data_user_user_metadata3 = data.user.user_metadata) === null || _data_user_user_metadata3 === void 0 ? void 0 : _data_user_user_metadata3.location,\n                        referralCode: (_data_user_user_metadata4 = data.user.user_metadata) === null || _data_user_user_metadata4 === void 0 ? void 0 : _data_user_user_metadata4.referral_code\n                    }, referrer);\n                }\n            } catch (profileError) {\n                console.error(\"Error creating user profile after verification:\", profileError);\n            // Don't fail the verification, but log the error\n            }\n        }\n        return data;\n    }\n    /**\n   * Resend email OTP\n   */ static async resendEmailOtp(email) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.resend({\n            type: \"signup\",\n            email\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Sign in with email and password\n   */ static async signIn(email, password) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Sign out the current user\n   */ static async signOut() {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut();\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Get the current user session\n   */ static async getSession() {\n        const { data: { session }, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return session;\n    }\n    /**\n   * Get the current user with improved error handling\n   */ static async getCurrentUser() {\n        try {\n            // First check if we have a valid session\n            const { data: { session }, error: sessionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (sessionError) {\n                console.error(\"Session error:\", sessionError);\n                return null;\n            }\n            if (!(session === null || session === void 0 ? void 0 : session.user)) {\n                console.log(\"No active session found\");\n                return null;\n            }\n            const user = session.user;\n            console.log(\"Found active session for user:\", user.email);\n            // Get additional user data from users table with retry logic\n            let retryCount = 0;\n            const maxRetries = 3;\n            while(retryCount < maxRetries){\n                try {\n                    const { data: profile, error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"*\").eq(\"id\", user.id).single();\n                    if (profileError) {\n                        if (profileError.code === \"PGRST116\") {\n                            var _user_user_metadata, _user_user_metadata1, _user_user_metadata2;\n                            // User profile doesn't exist, create it\n                            console.log(\"User profile not found, creating...\");\n                            const newProfile = {\n                                id: user.id,\n                                email: user.email,\n                                full_name: ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || null,\n                                phone: ((_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.phone) || null,\n                                location: ((_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.location) || null,\n                                role: \"user\",\n                                is_super_admin: false\n                            };\n                            const { data: createdProfile, error: createError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").insert(newProfile).select().single();\n                            if (createError) {\n                                console.error(\"Error creating user profile:\", createError);\n                                return {\n                                    id: user.id,\n                                    email: user.email\n                                };\n                            }\n                            return createdProfile;\n                        } else {\n                            throw profileError;\n                        }\n                    }\n                    return profile;\n                } catch (error) {\n                    retryCount++;\n                    console.error(\"Error fetching user profile (attempt \".concat(retryCount, \"):\"), error);\n                    if (retryCount >= maxRetries) {\n                        console.error(\"Max retries reached, returning basic user info\");\n                        return {\n                            id: user.id,\n                            email: user.email\n                        };\n                    }\n                    // Wait before retry\n                    await new Promise((resolve)=>setTimeout(resolve, 1000 * retryCount));\n                }\n            }\n            return null;\n        } catch (error) {\n            console.error(\"Error in getCurrentUser:\", error);\n            return null;\n        }\n    }\n    /**\n   * Update user profile\n   */ static async updateProfile(userId, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").update(updates).eq(\"id\", userId).select().single();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Reset password\n   */ static async resetPassword(email) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Update password\n   */ static async updatePassword(newPassword) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.updateUser({\n            password: newPassword\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Listen to auth state changes with improved handling\n   */ static onAuthStateChange(callback) {\n        return _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            var _session_user;\n            console.log(\"Auth state change detected:\", event, session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.email);\n            // Add a small delay to ensure state consistency\n            setTimeout(()=>{\n                callback(event, session);\n            }, 100);\n        });\n    }\n    /**\n   * Check if current session is valid\n   */ static async isSessionValid() {\n        try {\n            const { data: { session }, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (error || !session) {\n                return false;\n            }\n            // Check if token is expired\n            const now = Math.floor(Date.now() / 1000);\n            if (session.expires_at && session.expires_at < now) {\n                console.log(\"Session token expired\");\n                return false;\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Error checking session validity:\", error);\n            return false;\n        }\n    }\n    /**\n   * Refresh session if needed\n   */ static async refreshSession() {\n        try {\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.refreshSession();\n            if (error) {\n                console.error(\"Error refreshing session:\", error);\n                return false;\n            }\n            return !!data.session;\n        } catch (error) {\n            console.error(\"Error in refreshSession:\", error);\n            return false;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/auth.ts\n"));

/***/ })

});