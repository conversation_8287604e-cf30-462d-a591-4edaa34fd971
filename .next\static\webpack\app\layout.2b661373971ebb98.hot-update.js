"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: function() { return /* binding */ AuthService; }\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _services_adminSettings__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./services/adminSettings */ \"(app-pages-browser)/./src/lib/services/adminSettings.ts\");\n/* harmony import */ var _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./services/referralSystem */ \"(app-pages-browser)/./src/lib/services/referralSystem.ts\");\n\n\n\nclass AuthService {\n    /**\n   * Sign up a new user with OTP verification and referral processing\n   */ static async signUp(email, password, userData) {\n        // Check if email verification is required (with fallback to false if table doesn't exist)\n        let requireEmailVerification = false;\n        try {\n            requireEmailVerification = await _services_adminSettings__WEBPACK_IMPORTED_MODULE_1__.AdminSettingsService.getSetting(\"require_email_verification\") || false;\n        } catch (error) {\n            // Gracefully handle admin settings access failures\n            console.warn(\"Could not fetch email verification setting, defaulting to false:\", error);\n            requireEmailVerification = false;\n            // If it's a 406 error or RLS policy error, it means the table exists but access is restricted\n            // This is expected during signup when user is not authenticated yet\n            if (error instanceof Error && (error.message.includes(\"406\") || error.message.includes(\"policy\"))) {\n                console.info(\"Admin settings access restricted during signup - using default values\");\n            }\n        }\n        // Validate referral code if provided\n        let referrer = null;\n        if (userData === null || userData === void 0 ? void 0 : userData.referralCode) {\n            try {\n                referrer = await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.validateReferralCode(userData.referralCode);\n                if (!referrer) {\n                    throw new Error(\"Invalid referral code\");\n                }\n            } catch (error) {\n                console.warn(\"Could not validate referral code:\", error);\n            // Don't fail signup for referral code issues, just log the warning\n            }\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    ...userData,\n                    referral_code: userData === null || userData === void 0 ? void 0 : userData.referralCode,\n                    full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n                    phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n                    location: userData === null || userData === void 0 ? void 0 : userData.location,\n                    referrer_id: referrer === null || referrer === void 0 ? void 0 : referrer.id\n                }\n            }\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        // If user is created and email verification is NOT required, create profile immediately\n        if (data.user && !requireEmailVerification) {\n            try {\n                // Create basic profile first without complex operations\n                await this.createBasicUserProfile(data.user, userData);\n                // Defer complex operations like referral hierarchy placement\n                if (referrer) {\n                    // Schedule referral placement for later (don't block signup)\n                    this.scheduleReferralPlacement(data.user.id, referrer.id).catch((error)=>{\n                        console.error(\"Failed to schedule referral placement:\", error);\n                    });\n                }\n            } catch (profileError) {\n                console.error(\"Error creating user profile:\", profileError);\n            // Don't fail the signup, but log the error\n            }\n        }\n        return {\n            data,\n            requireEmailVerification,\n            referrer\n        };\n    }\n    /**\n   * Create basic user profile without complex operations (fast)\n   */ static async createBasicUserProfile(authUser, userData) {\n        // Check if admin client is available\n        if (!_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin) {\n            throw new Error(\"Admin client not available - missing SUPABASE_SERVICE_ROLE_KEY\");\n        }\n        // Use service role client to bypass RLS for user creation\n        // Create minimal profile to avoid trigger delays\n        const { error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"users\").insert({\n            id: authUser.id,\n            email: authUser.email,\n            full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n            phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n            location: userData === null || userData === void 0 ? void 0 : userData.location,\n            user_type: \"user\",\n            role: \"user\",\n            is_super_admin: false\n        });\n        if (profileError) {\n            throw new Error(\"Failed to create user profile: \".concat(profileError.message));\n        }\n    }\n    /**\n   * Schedule referral placement for later processing (non-blocking)\n   */ static async scheduleReferralPlacement(userId, referrerId) {\n        try {\n            // Use a timeout to defer this operation\n            setTimeout(async ()=>{\n                try {\n                    await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.placeUserInHierarchy(userId, referrerId);\n                    console.log(\"Referral placement completed for user:\", userId);\n                } catch (error) {\n                    console.error(\"Deferred referral placement failed:\", error);\n                }\n            }, 2000) // 2 second delay\n            ;\n        } catch (error) {\n            console.error(\"Failed to schedule referral placement:\", error);\n        }\n    }\n    /**\n   * Create user profile in public.users table (legacy method with full operations)\n   */ static async createUserProfile(authUser, userData, referrer) {\n        // Check if admin client is available\n        if (!_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin) {\n            throw new Error(\"Admin client not available - missing SUPABASE_SERVICE_ROLE_KEY\");\n        }\n        // Use service role client to bypass RLS for user creation\n        const { error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"users\").insert({\n            id: authUser.id,\n            email: authUser.email,\n            full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n            phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n            location: userData === null || userData === void 0 ? void 0 : userData.location,\n            user_type: \"user\",\n            referral_level: referrer ? (referrer.referral_level || 0) + 1 : 0,\n            referred_by_id: (referrer === null || referrer === void 0 ? void 0 : referrer.id) || null\n        });\n        if (profileError) {\n            throw new Error(\"Failed to create user profile: \".concat(profileError.message));\n        }\n        // If user has a referrer, place them in hierarchy\n        if (referrer) {\n            try {\n                await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.placeUserInHierarchy(authUser.id, referrer.id);\n            } catch (referralError) {\n                console.error(\"Failed to place user in referral hierarchy:\", referralError);\n            // Don't fail the profile creation, but log the error\n            }\n        }\n    }\n    /**\n   * Verify email OTP and process referral placement\n   */ static async verifyEmailOtp(email, token) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.verifyOtp({\n            email,\n            token,\n            type: \"signup\"\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        // After successful verification, create user profile if it doesn't exist\n        if (data.user) {\n            try {\n                // Check if user profile already exists\n                const { data: existingUser } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"id\").eq(\"id\", data.user.id).single();\n                if (!existingUser) {\n                    var _data_user_user_metadata, _data_user_user_metadata1, _data_user_user_metadata2, _data_user_user_metadata3, _data_user_user_metadata4;\n                    // Get referrer if referral code exists\n                    let referrer = null;\n                    if ((_data_user_user_metadata = data.user.user_metadata) === null || _data_user_user_metadata === void 0 ? void 0 : _data_user_user_metadata.referral_code) {\n                        referrer = await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.validateReferralCode(data.user.user_metadata.referral_code);\n                    }\n                    // Create user profile\n                    await this.createUserProfile(data.user, {\n                        full_name: (_data_user_user_metadata1 = data.user.user_metadata) === null || _data_user_user_metadata1 === void 0 ? void 0 : _data_user_user_metadata1.full_name,\n                        phone: (_data_user_user_metadata2 = data.user.user_metadata) === null || _data_user_user_metadata2 === void 0 ? void 0 : _data_user_user_metadata2.phone,\n                        location: (_data_user_user_metadata3 = data.user.user_metadata) === null || _data_user_user_metadata3 === void 0 ? void 0 : _data_user_user_metadata3.location,\n                        referralCode: (_data_user_user_metadata4 = data.user.user_metadata) === null || _data_user_user_metadata4 === void 0 ? void 0 : _data_user_user_metadata4.referral_code\n                    }, referrer);\n                }\n            } catch (profileError) {\n                console.error(\"Error creating user profile after verification:\", profileError);\n            // Don't fail the verification, but log the error\n            }\n        }\n        return data;\n    }\n    /**\n   * Resend email OTP\n   */ static async resendEmailOtp(email) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.resend({\n            type: \"signup\",\n            email\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Sign in with email and password\n   */ static async signIn(email, password) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Sign out the current user\n   */ static async signOut() {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut();\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Get the current user session\n   */ static async getSession() {\n        const { data: { session }, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return session;\n    }\n    /**\n   * Get the current user with improved error handling\n   */ static async getCurrentUser() {\n        try {\n            // First check if we have a valid session\n            const { data: { session }, error: sessionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (sessionError) {\n                console.error(\"Session error:\", sessionError);\n                return null;\n            }\n            if (!(session === null || session === void 0 ? void 0 : session.user)) {\n                console.log(\"No active session found\");\n                return null;\n            }\n            const user = session.user;\n            console.log(\"Found active session for user:\", user.email);\n            // Get additional user data from users table with retry logic\n            let retryCount = 0;\n            const maxRetries = 3;\n            while(retryCount < maxRetries){\n                try {\n                    const { data: profile, error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"*\").eq(\"id\", user.id).single();\n                    if (profileError) {\n                        if (profileError.code === \"PGRST116\") {\n                            var _user_user_metadata, _user_user_metadata1, _user_user_metadata2;\n                            // User profile doesn't exist, create it\n                            console.log(\"User profile not found, creating...\");\n                            const newProfile = {\n                                id: user.id,\n                                email: user.email,\n                                full_name: ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || null,\n                                phone: ((_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.phone) || null,\n                                location: ((_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.location) || null,\n                                role: \"user\",\n                                is_super_admin: false\n                            };\n                            const { data: createdProfile, error: createError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").insert(newProfile).select().single();\n                            if (createError) {\n                                console.error(\"Error creating user profile:\", createError);\n                                return {\n                                    id: user.id,\n                                    email: user.email\n                                };\n                            }\n                            return createdProfile;\n                        } else {\n                            throw profileError;\n                        }\n                    }\n                    return profile;\n                } catch (error) {\n                    retryCount++;\n                    console.error(\"Error fetching user profile (attempt \".concat(retryCount, \"):\"), error);\n                    if (retryCount >= maxRetries) {\n                        console.error(\"Max retries reached, returning basic user info\");\n                        return {\n                            id: user.id,\n                            email: user.email\n                        };\n                    }\n                    // Wait before retry\n                    await new Promise((resolve)=>setTimeout(resolve, 1000 * retryCount));\n                }\n            }\n            return null;\n        } catch (error) {\n            console.error(\"Error in getCurrentUser:\", error);\n            return null;\n        }\n    }\n    /**\n   * Update user profile\n   */ static async updateProfile(userId, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").update(updates).eq(\"id\", userId).select().single();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Reset password\n   */ static async resetPassword(email) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Update password\n   */ static async updatePassword(newPassword) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.updateUser({\n            password: newPassword\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Listen to auth state changes with improved handling\n   */ static onAuthStateChange(callback) {\n        return _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            var _session_user;\n            console.log(\"Auth state change detected:\", event, session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.email);\n            // Add a small delay to ensure state consistency\n            setTimeout(()=>{\n                callback(event, session);\n            }, 100);\n        });\n    }\n    /**\n   * Check if current session is valid\n   */ static async isSessionValid() {\n        try {\n            const { data: { session }, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (error || !session) {\n                return false;\n            }\n            // Check if token is expired\n            const now = Math.floor(Date.now() / 1000);\n            if (session.expires_at && session.expires_at < now) {\n                console.log(\"Session token expired\");\n                return false;\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Error checking session validity:\", error);\n            return false;\n        }\n    }\n    /**\n   * Refresh session if needed\n   */ static async refreshSession() {\n        try {\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.refreshSession();\n            if (error) {\n                console.error(\"Error refreshing session:\", error);\n                return false;\n            }\n            return !!data.session;\n        } catch (error) {\n            console.error(\"Error in refreshSession:\", error);\n            return false;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvYXV0aC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQW9EO0FBRVc7QUFDRTtBQVcxRCxNQUFNSTtJQUNYOztHQUVDLEdBQ0QsYUFBYUMsT0FBT0MsS0FBYSxFQUFFQyxRQUFnQixFQUFFQyxRQUFvRCxFQUFFO1FBQ3pHLDBGQUEwRjtRQUMxRixJQUFJQywyQkFBMkI7UUFDL0IsSUFBSTtZQUNGQSwyQkFBMkIsTUFBTVAseUVBQW9CQSxDQUFDUSxVQUFVLENBQUMsaUNBQWlDO1FBQ3BHLEVBQUUsT0FBT0MsT0FBTztZQUNkLG1EQUFtRDtZQUNuREMsUUFBUUMsSUFBSSxDQUFDLG9FQUFvRUY7WUFDakZGLDJCQUEyQjtZQUUzQiw4RkFBOEY7WUFDOUYsb0VBQW9FO1lBQ3BFLElBQUlFLGlCQUFpQkcsU0FBVUgsQ0FBQUEsTUFBTUksT0FBTyxDQUFDQyxRQUFRLENBQUMsVUFBVUwsTUFBTUksT0FBTyxDQUFDQyxRQUFRLENBQUMsU0FBUSxHQUFJO2dCQUNqR0osUUFBUUssSUFBSSxDQUFDO1lBQ2Y7UUFDRjtRQUVBLHFDQUFxQztRQUNyQyxJQUFJQyxXQUF3QjtRQUM1QixJQUFJVixxQkFBQUEsK0JBQUFBLFNBQVVXLFlBQVksRUFBRTtZQUMxQixJQUFJO2dCQUNGRCxXQUFXLE1BQU1mLDJFQUFxQkEsQ0FBQ2lCLG9CQUFvQixDQUFDWixTQUFTVyxZQUFZO2dCQUNqRixJQUFJLENBQUNELFVBQVU7b0JBQ2IsTUFBTSxJQUFJSixNQUFNO2dCQUNsQjtZQUNGLEVBQUUsT0FBT0gsT0FBTztnQkFDZEMsUUFBUUMsSUFBSSxDQUFDLHFDQUFxQ0Y7WUFDbEQsbUVBQW1FO1lBQ3JFO1FBQ0Y7UUFFQSxNQUFNLEVBQUVVLElBQUksRUFBRVYsS0FBSyxFQUFFLEdBQUcsTUFBTVgsK0NBQVFBLENBQUNzQixJQUFJLENBQUNqQixNQUFNLENBQUM7WUFDakRDO1lBQ0FDO1lBQ0FnQixTQUFTO2dCQUNQRixNQUFNO29CQUNKLEdBQUdiLFFBQVE7b0JBQ1hnQixhQUFhLEVBQUVoQixxQkFBQUEsK0JBQUFBLFNBQVVXLFlBQVk7b0JBQ3JDTSxTQUFTLEVBQUVqQixxQkFBQUEsK0JBQUFBLFNBQVVpQixTQUFTO29CQUM5QkMsS0FBSyxFQUFFbEIscUJBQUFBLCtCQUFBQSxTQUFVa0IsS0FBSztvQkFDdEJDLFFBQVEsRUFBRW5CLHFCQUFBQSwrQkFBQUEsU0FBVW1CLFFBQVE7b0JBQzVCQyxXQUFXLEVBQUVWLHFCQUFBQSwrQkFBQUEsU0FBVVcsRUFBRTtnQkFDM0I7WUFFRjtRQUNGO1FBRUEsSUFBSWxCLE9BQU87WUFDVCxNQUFNLElBQUlHLE1BQU1ILE1BQU1JLE9BQU87UUFDL0I7UUFFQSx3RkFBd0Y7UUFDeEYsSUFBSU0sS0FBS1MsSUFBSSxJQUFJLENBQUNyQiwwQkFBMEI7WUFDMUMsSUFBSTtnQkFDRix3REFBd0Q7Z0JBQ3hELE1BQU0sSUFBSSxDQUFDc0Isc0JBQXNCLENBQUNWLEtBQUtTLElBQUksRUFBRXRCO2dCQUU3Qyw2REFBNkQ7Z0JBQzdELElBQUlVLFVBQVU7b0JBQ1osNkRBQTZEO29CQUM3RCxJQUFJLENBQUNjLHlCQUF5QixDQUFDWCxLQUFLUyxJQUFJLENBQUNELEVBQUUsRUFBRVgsU0FBU1csRUFBRSxFQUFFSSxLQUFLLENBQUN0QixDQUFBQTt3QkFDOURDLFFBQVFELEtBQUssQ0FBQywwQ0FBMENBO29CQUMxRDtnQkFDRjtZQUNGLEVBQUUsT0FBT3VCLGNBQWM7Z0JBQ3JCdEIsUUFBUUQsS0FBSyxDQUFDLGdDQUFnQ3VCO1lBQzlDLDJDQUEyQztZQUM3QztRQUNGO1FBRUEsT0FBTztZQUFFYjtZQUFNWjtZQUEwQlM7UUFBUztJQUNwRDtJQUVBOztHQUVDLEdBQ0QsYUFBcUJhLHVCQUF1QkksUUFBYSxFQUFFM0IsUUFBb0QsRUFBRTtRQUMvRyxxQ0FBcUM7UUFDckMsSUFBSSxDQUFDUCxvREFBYUEsRUFBRTtZQUNsQixNQUFNLElBQUlhLE1BQU07UUFDbEI7UUFFQSwwREFBMEQ7UUFDMUQsaURBQWlEO1FBQ2pELE1BQU0sRUFBRUgsT0FBT3VCLFlBQVksRUFBRSxHQUFHLE1BQU1qQyxvREFBYUEsQ0FDaERtQyxJQUFJLENBQUMsU0FDTEMsTUFBTSxDQUFDO1lBQ05SLElBQUlNLFNBQVNOLEVBQUU7WUFDZnZCLE9BQU82QixTQUFTN0IsS0FBSztZQUNyQm1CLFNBQVMsRUFBRWpCLHFCQUFBQSwrQkFBQUEsU0FBVWlCLFNBQVM7WUFDOUJDLEtBQUssRUFBRWxCLHFCQUFBQSwrQkFBQUEsU0FBVWtCLEtBQUs7WUFDdEJDLFFBQVEsRUFBRW5CLHFCQUFBQSwrQkFBQUEsU0FBVW1CLFFBQVE7WUFDNUJXLFdBQVc7WUFDWEMsTUFBTTtZQUNOQyxnQkFBZ0I7UUFFbEI7UUFFRixJQUFJTixjQUFjO1lBQ2hCLE1BQU0sSUFBSXBCLE1BQU0sa0NBQXVELE9BQXJCb0IsYUFBYW5CLE9BQU87UUFDeEU7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBcUJpQiwwQkFBMEJTLE1BQWMsRUFBRUMsVUFBa0IsRUFBRTtRQUNqRixJQUFJO1lBQ0Ysd0NBQXdDO1lBQ3hDQyxXQUFXO2dCQUNULElBQUk7b0JBQ0YsTUFBTXhDLDJFQUFxQkEsQ0FBQ3lDLG9CQUFvQixDQUFDSCxRQUFRQztvQkFDekQ5QixRQUFRaUMsR0FBRyxDQUFDLDBDQUEwQ0o7Z0JBQ3hELEVBQUUsT0FBTzlCLE9BQU87b0JBQ2RDLFFBQVFELEtBQUssQ0FBQyx1Q0FBdUNBO2dCQUN2RDtZQUNGLEdBQUcsTUFBTSxpQkFBaUI7O1FBQzVCLEVBQUUsT0FBT0EsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsMENBQTBDQTtRQUMxRDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxhQUFxQm1DLGtCQUFrQlgsUUFBYSxFQUFFM0IsUUFBb0QsRUFBRVUsUUFBc0IsRUFBRTtRQUNsSSxxQ0FBcUM7UUFDckMsSUFBSSxDQUFDakIsb0RBQWFBLEVBQUU7WUFDbEIsTUFBTSxJQUFJYSxNQUFNO1FBQ2xCO1FBRUEsMERBQTBEO1FBQzFELE1BQU0sRUFBRUgsT0FBT3VCLFlBQVksRUFBRSxHQUFHLE1BQU1qQyxvREFBYUEsQ0FDaERtQyxJQUFJLENBQUMsU0FDTEMsTUFBTSxDQUFDO1lBQ05SLElBQUlNLFNBQVNOLEVBQUU7WUFDZnZCLE9BQU82QixTQUFTN0IsS0FBSztZQUNyQm1CLFNBQVMsRUFBRWpCLHFCQUFBQSwrQkFBQUEsU0FBVWlCLFNBQVM7WUFDOUJDLEtBQUssRUFBRWxCLHFCQUFBQSwrQkFBQUEsU0FBVWtCLEtBQUs7WUFDdEJDLFFBQVEsRUFBRW5CLHFCQUFBQSwrQkFBQUEsU0FBVW1CLFFBQVE7WUFDNUJXLFdBQVc7WUFDWFMsZ0JBQWdCN0IsV0FBVyxDQUFDQSxTQUFTNkIsY0FBYyxJQUFJLEtBQUssSUFBSTtZQUNoRUMsZ0JBQWdCOUIsQ0FBQUEscUJBQUFBLCtCQUFBQSxTQUFVVyxFQUFFLEtBQUk7UUFDbEM7UUFFRixJQUFJSyxjQUFjO1lBQ2hCLE1BQU0sSUFBSXBCLE1BQU0sa0NBQXVELE9BQXJCb0IsYUFBYW5CLE9BQU87UUFDeEU7UUFFQSxrREFBa0Q7UUFDbEQsSUFBSUcsVUFBVTtZQUNaLElBQUk7Z0JBQ0YsTUFBTWYsMkVBQXFCQSxDQUFDeUMsb0JBQW9CLENBQUNULFNBQVNOLEVBQUUsRUFBRVgsU0FBU1csRUFBRTtZQUMzRSxFQUFFLE9BQU9vQixlQUFlO2dCQUN0QnJDLFFBQVFELEtBQUssQ0FBQywrQ0FBK0NzQztZQUM3RCxxREFBcUQ7WUFDdkQ7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxhQUFhQyxlQUFlNUMsS0FBYSxFQUFFNkMsS0FBYSxFQUFFO1FBQ3hELE1BQU0sRUFBRTlCLElBQUksRUFBRVYsS0FBSyxFQUFFLEdBQUcsTUFBTVgsK0NBQVFBLENBQUNzQixJQUFJLENBQUM4QixTQUFTLENBQUM7WUFDcEQ5QztZQUNBNkM7WUFDQUUsTUFBTTtRQUNSO1FBRUEsSUFBSTFDLE9BQU87WUFDVCxNQUFNLElBQUlHLE1BQU1ILE1BQU1JLE9BQU87UUFDL0I7UUFFQSx5RUFBeUU7UUFDekUsSUFBSU0sS0FBS1MsSUFBSSxFQUFFO1lBQ2IsSUFBSTtnQkFDRix1Q0FBdUM7Z0JBQ3ZDLE1BQU0sRUFBRVQsTUFBTWlDLFlBQVksRUFBRSxHQUFHLE1BQU10RCwrQ0FBUUEsQ0FDMUNvQyxJQUFJLENBQUMsU0FDTG1CLE1BQU0sQ0FBQyxNQUNQQyxFQUFFLENBQUMsTUFBTW5DLEtBQUtTLElBQUksQ0FBQ0QsRUFBRSxFQUNyQjRCLE1BQU07Z0JBRVQsSUFBSSxDQUFDSCxjQUFjO3dCQUdiakMsMEJBTVNBLDJCQUNKQSwyQkFDR0EsMkJBQ0lBO29CQVhoQix1Q0FBdUM7b0JBQ3ZDLElBQUlILFdBQXdCO29CQUM1QixLQUFJRywyQkFBQUEsS0FBS1MsSUFBSSxDQUFDNEIsYUFBYSxjQUF2QnJDLCtDQUFBQSx5QkFBeUJHLGFBQWEsRUFBRTt3QkFDMUNOLFdBQVcsTUFBTWYsMkVBQXFCQSxDQUFDaUIsb0JBQW9CLENBQUNDLEtBQUtTLElBQUksQ0FBQzRCLGFBQWEsQ0FBQ2xDLGFBQWE7b0JBQ25HO29CQUVBLHNCQUFzQjtvQkFDdEIsTUFBTSxJQUFJLENBQUNzQixpQkFBaUIsQ0FBQ3pCLEtBQUtTLElBQUksRUFBRTt3QkFDdENMLFNBQVMsR0FBRUosNEJBQUFBLEtBQUtTLElBQUksQ0FBQzRCLGFBQWEsY0FBdkJyQyxnREFBQUEsMEJBQXlCSSxTQUFTO3dCQUM3Q0MsS0FBSyxHQUFFTCw0QkFBQUEsS0FBS1MsSUFBSSxDQUFDNEIsYUFBYSxjQUF2QnJDLGdEQUFBQSwwQkFBeUJLLEtBQUs7d0JBQ3JDQyxRQUFRLEdBQUVOLDRCQUFBQSxLQUFLUyxJQUFJLENBQUM0QixhQUFhLGNBQXZCckMsZ0RBQUFBLDBCQUF5Qk0sUUFBUTt3QkFDM0NSLFlBQVksR0FBRUUsNEJBQUFBLEtBQUtTLElBQUksQ0FBQzRCLGFBQWEsY0FBdkJyQyxnREFBQUEsMEJBQXlCRyxhQUFhO29CQUN0RCxHQUFHTjtnQkFDTDtZQUNGLEVBQUUsT0FBT2dCLGNBQWM7Z0JBQ3JCdEIsUUFBUUQsS0FBSyxDQUFDLG1EQUFtRHVCO1lBQ2pFLGlEQUFpRDtZQUNuRDtRQUNGO1FBRUEsT0FBT2I7SUFDVDtJQUVBOztHQUVDLEdBQ0QsYUFBYXNDLGVBQWVyRCxLQUFhLEVBQUU7UUFDekMsTUFBTSxFQUFFZSxJQUFJLEVBQUVWLEtBQUssRUFBRSxHQUFHLE1BQU1YLCtDQUFRQSxDQUFDc0IsSUFBSSxDQUFDc0MsTUFBTSxDQUFDO1lBQ2pEUCxNQUFNO1lBQ04vQztRQUNGO1FBRUEsSUFBSUssT0FBTztZQUNULE1BQU0sSUFBSUcsTUFBTUgsTUFBTUksT0FBTztRQUMvQjtRQUVBLE9BQU9NO0lBQ1Q7SUFFQTs7R0FFQyxHQUNELGFBQWF3QyxPQUFPdkQsS0FBYSxFQUFFQyxRQUFnQixFQUFFO1FBQ25ELE1BQU0sRUFBRWMsSUFBSSxFQUFFVixLQUFLLEVBQUUsR0FBRyxNQUFNWCwrQ0FBUUEsQ0FBQ3NCLElBQUksQ0FBQ3dDLGtCQUFrQixDQUFDO1lBQzdEeEQ7WUFDQUM7UUFDRjtRQUVBLElBQUlJLE9BQU87WUFDVCxNQUFNLElBQUlHLE1BQU1ILE1BQU1JLE9BQU87UUFDL0I7UUFFQSxPQUFPTTtJQUNUO0lBRUE7O0dBRUMsR0FDRCxhQUFhMEMsVUFBVTtRQUNyQixNQUFNLEVBQUVwRCxLQUFLLEVBQUUsR0FBRyxNQUFNWCwrQ0FBUUEsQ0FBQ3NCLElBQUksQ0FBQ3lDLE9BQU87UUFFN0MsSUFBSXBELE9BQU87WUFDVCxNQUFNLElBQUlHLE1BQU1ILE1BQU1JLE9BQU87UUFDL0I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBYWlELGFBQWE7UUFDeEIsTUFBTSxFQUFFM0MsTUFBTSxFQUFFNEMsT0FBTyxFQUFFLEVBQUV0RCxLQUFLLEVBQUUsR0FBRyxNQUFNWCwrQ0FBUUEsQ0FBQ3NCLElBQUksQ0FBQzBDLFVBQVU7UUFFbkUsSUFBSXJELE9BQU87WUFDVCxNQUFNLElBQUlHLE1BQU1ILE1BQU1JLE9BQU87UUFDL0I7UUFFQSxPQUFPa0Q7SUFDVDtJQUVBOztHQUVDLEdBQ0QsYUFBYUMsaUJBQTJDO1FBQ3RELElBQUk7WUFDRix5Q0FBeUM7WUFDekMsTUFBTSxFQUFFN0MsTUFBTSxFQUFFNEMsT0FBTyxFQUFFLEVBQUV0RCxPQUFPd0QsWUFBWSxFQUFFLEdBQUcsTUFBTW5FLCtDQUFRQSxDQUFDc0IsSUFBSSxDQUFDMEMsVUFBVTtZQUVqRixJQUFJRyxjQUFjO2dCQUNoQnZELFFBQVFELEtBQUssQ0FBQyxrQkFBa0J3RDtnQkFDaEMsT0FBTztZQUNUO1lBRUEsSUFBSSxFQUFDRixvQkFBQUEsOEJBQUFBLFFBQVNuQyxJQUFJLEdBQUU7Z0JBQ2xCbEIsUUFBUWlDLEdBQUcsQ0FBQztnQkFDWixPQUFPO1lBQ1Q7WUFFQSxNQUFNZixPQUFPbUMsUUFBUW5DLElBQUk7WUFDekJsQixRQUFRaUMsR0FBRyxDQUFDLGtDQUFrQ2YsS0FBS3hCLEtBQUs7WUFFeEQsNkRBQTZEO1lBQzdELElBQUk4RCxhQUFhO1lBQ2pCLE1BQU1DLGFBQWE7WUFFbkIsTUFBT0QsYUFBYUMsV0FBWTtnQkFDOUIsSUFBSTtvQkFDRixNQUFNLEVBQUVoRCxNQUFNaUQsT0FBTyxFQUFFM0QsT0FBT3VCLFlBQVksRUFBRSxHQUFHLE1BQU1sQywrQ0FBUUEsQ0FDMURvQyxJQUFJLENBQUMsU0FDTG1CLE1BQU0sQ0FBQyxLQUNQQyxFQUFFLENBQUMsTUFBTTFCLEtBQUtELEVBQUUsRUFDaEI0QixNQUFNO29CQUVULElBQUl2QixjQUFjO3dCQUNoQixJQUFJQSxhQUFhcUMsSUFBSSxLQUFLLFlBQVk7Z0NBTXZCekMscUJBQ0pBLHNCQUNHQTs0QkFQWix3Q0FBd0M7NEJBQ3hDbEIsUUFBUWlDLEdBQUcsQ0FBQzs0QkFDWixNQUFNMkIsYUFBYTtnQ0FDakIzQyxJQUFJQyxLQUFLRCxFQUFFO2dDQUNYdkIsT0FBT3dCLEtBQUt4QixLQUFLO2dDQUNqQm1CLFdBQVdLLEVBQUFBLHNCQUFBQSxLQUFLNEIsYUFBYSxjQUFsQjVCLDBDQUFBQSxvQkFBb0JMLFNBQVMsS0FBSTtnQ0FDNUNDLE9BQU9JLEVBQUFBLHVCQUFBQSxLQUFLNEIsYUFBYSxjQUFsQjVCLDJDQUFBQSxxQkFBb0JKLEtBQUssS0FBSTtnQ0FDcENDLFVBQVVHLEVBQUFBLHVCQUFBQSxLQUFLNEIsYUFBYSxjQUFsQjVCLDJDQUFBQSxxQkFBb0JILFFBQVEsS0FBSTtnQ0FDMUNZLE1BQU07Z0NBQ05DLGdCQUFnQjs0QkFDbEI7NEJBRUEsTUFBTSxFQUFFbkIsTUFBTW9ELGNBQWMsRUFBRTlELE9BQU8rRCxXQUFXLEVBQUUsR0FBRyxNQUFNMUUsK0NBQVFBLENBQ2hFb0MsSUFBSSxDQUFDLFNBQ0xDLE1BQU0sQ0FBQ21DLFlBQ1BqQixNQUFNLEdBQ05FLE1BQU07NEJBRVQsSUFBSWlCLGFBQWE7Z0NBQ2Y5RCxRQUFRRCxLQUFLLENBQUMsZ0NBQWdDK0Q7Z0NBQzlDLE9BQU87b0NBQ0w3QyxJQUFJQyxLQUFLRCxFQUFFO29DQUNYdkIsT0FBT3dCLEtBQUt4QixLQUFLO2dDQUNuQjs0QkFDRjs0QkFFQSxPQUFPbUU7d0JBQ1QsT0FBTzs0QkFDTCxNQUFNdkM7d0JBQ1I7b0JBQ0Y7b0JBRUEsT0FBT29DO2dCQUNULEVBQUUsT0FBTzNELE9BQU87b0JBQ2R5RDtvQkFDQXhELFFBQVFELEtBQUssQ0FBQyx3Q0FBbUQsT0FBWHlELFlBQVcsT0FBS3pEO29CQUV0RSxJQUFJeUQsY0FBY0MsWUFBWTt3QkFDNUJ6RCxRQUFRRCxLQUFLLENBQUM7d0JBQ2QsT0FBTzs0QkFDTGtCLElBQUlDLEtBQUtELEVBQUU7NEJBQ1h2QixPQUFPd0IsS0FBS3hCLEtBQUs7d0JBQ25CO29CQUNGO29CQUVBLG9CQUFvQjtvQkFDcEIsTUFBTSxJQUFJcUUsUUFBUUMsQ0FBQUEsVUFBV2pDLFdBQVdpQyxTQUFTLE9BQU9SO2dCQUMxRDtZQUNGO1lBRUEsT0FBTztRQUNULEVBQUUsT0FBT3pELE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7WUFDMUMsT0FBTztRQUNUO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWFrRSxjQUFjcEMsTUFBYyxFQUFFcUMsT0FBc0IsRUFBRTtRQUNqRSxNQUFNLEVBQUV6RCxJQUFJLEVBQUVWLEtBQUssRUFBRSxHQUFHLE1BQU1YLCtDQUFRQSxDQUNuQ29DLElBQUksQ0FBQyxTQUNMMkMsTUFBTSxDQUFDRCxTQUNQdEIsRUFBRSxDQUFDLE1BQU1mLFFBQ1RjLE1BQU0sR0FDTkUsTUFBTTtRQUVULElBQUk5QyxPQUFPO1lBQ1QsTUFBTSxJQUFJRyxNQUFNSCxNQUFNSSxPQUFPO1FBQy9CO1FBRUEsT0FBT007SUFDVDtJQUVBOztHQUVDLEdBQ0QsYUFBYTJELGNBQWMxRSxLQUFhLEVBQUU7UUFDeEMsTUFBTSxFQUFFSyxLQUFLLEVBQUUsR0FBRyxNQUFNWCwrQ0FBUUEsQ0FBQ3NCLElBQUksQ0FBQzJELHFCQUFxQixDQUFDM0UsT0FBTztZQUNqRTRFLFlBQVksR0FBMEIsT0FBdkJDLE9BQU94RCxRQUFRLENBQUN5RCxNQUFNLEVBQUM7UUFDeEM7UUFFQSxJQUFJekUsT0FBTztZQUNULE1BQU0sSUFBSUcsTUFBTUgsTUFBTUksT0FBTztRQUMvQjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxhQUFhc0UsZUFBZUMsV0FBbUIsRUFBRTtRQUMvQyxNQUFNLEVBQUUzRSxLQUFLLEVBQUUsR0FBRyxNQUFNWCwrQ0FBUUEsQ0FBQ3NCLElBQUksQ0FBQ2lFLFVBQVUsQ0FBQztZQUMvQ2hGLFVBQVUrRTtRQUNaO1FBRUEsSUFBSTNFLE9BQU87WUFDVCxNQUFNLElBQUlHLE1BQU1ILE1BQU1JLE9BQU87UUFDL0I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsT0FBT3lFLGtCQUFrQkMsUUFBK0MsRUFBRTtRQUN4RSxPQUFPekYsK0NBQVFBLENBQUNzQixJQUFJLENBQUNrRSxpQkFBaUIsQ0FBQyxPQUFPRSxPQUFPekI7Z0JBQ0RBO1lBQWxEckQsUUFBUWlDLEdBQUcsQ0FBQywrQkFBK0I2QyxPQUFPekIsb0JBQUFBLCtCQUFBQSxnQkFBQUEsUUFBU25DLElBQUksY0FBYm1DLG9DQUFBQSxjQUFlM0QsS0FBSztZQUV0RSxnREFBZ0Q7WUFDaERxQyxXQUFXO2dCQUNUOEMsU0FBU0MsT0FBT3pCO1lBQ2xCLEdBQUc7UUFDTDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxhQUFhMEIsaUJBQW1DO1FBQzlDLElBQUk7WUFDRixNQUFNLEVBQUV0RSxNQUFNLEVBQUU0QyxPQUFPLEVBQUUsRUFBRXRELEtBQUssRUFBRSxHQUFHLE1BQU1YLCtDQUFRQSxDQUFDc0IsSUFBSSxDQUFDMEMsVUFBVTtZQUVuRSxJQUFJckQsU0FBUyxDQUFDc0QsU0FBUztnQkFDckIsT0FBTztZQUNUO1lBRUEsNEJBQTRCO1lBQzVCLE1BQU0yQixNQUFNQyxLQUFLQyxLQUFLLENBQUNDLEtBQUtILEdBQUcsS0FBSztZQUNwQyxJQUFJM0IsUUFBUStCLFVBQVUsSUFBSS9CLFFBQVErQixVQUFVLEdBQUdKLEtBQUs7Z0JBQ2xEaEYsUUFBUWlDLEdBQUcsQ0FBQztnQkFDWixPQUFPO1lBQ1Q7WUFFQSxPQUFPO1FBQ1QsRUFBRSxPQUFPbEMsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsb0NBQW9DQTtZQUNsRCxPQUFPO1FBQ1Q7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBYXNGLGlCQUFtQztRQUM5QyxJQUFJO1lBQ0YsTUFBTSxFQUFFNUUsSUFBSSxFQUFFVixLQUFLLEVBQUUsR0FBRyxNQUFNWCwrQ0FBUUEsQ0FBQ3NCLElBQUksQ0FBQzJFLGNBQWM7WUFFMUQsSUFBSXRGLE9BQU87Z0JBQ1RDLFFBQVFELEtBQUssQ0FBQyw2QkFBNkJBO2dCQUMzQyxPQUFPO1lBQ1Q7WUFFQSxPQUFPLENBQUMsQ0FBQ1UsS0FBSzRDLE9BQU87UUFDdkIsRUFBRSxPQUFPdEQsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNEJBQTRCQTtZQUMxQyxPQUFPO1FBQ1Q7SUFDRjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9saWIvYXV0aC50cz82NjkyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN1cGFiYXNlLCBzdXBhYmFzZUFkbWluIH0gZnJvbSAnLi9zdXBhYmFzZSdcbmltcG9ydCB7IFVzZXIgfSBmcm9tICdAL3R5cGVzJ1xuaW1wb3J0IHsgQWRtaW5TZXR0aW5nc1NlcnZpY2UgfSBmcm9tICcuL3NlcnZpY2VzL2FkbWluU2V0dGluZ3MnXG5pbXBvcnQgeyBSZWZlcnJhbFN5c3RlbVNlcnZpY2UgfSBmcm9tICcuL3NlcnZpY2VzL3JlZmVycmFsU3lzdGVtJ1xuXG5leHBvcnQgaW50ZXJmYWNlIEF1dGhVc2VyIHtcbiAgaWQ6IHN0cmluZ1xuICBlbWFpbDogc3RyaW5nXG4gIGZ1bGxfbmFtZT86IHN0cmluZ1xuICBwaG9uZT86IHN0cmluZ1xuICBsb2NhdGlvbj86IHN0cmluZ1xuICBhdmF0YXJfdXJsPzogc3RyaW5nXG59XG5cbmV4cG9ydCBjbGFzcyBBdXRoU2VydmljZSB7XG4gIC8qKlxuICAgKiBTaWduIHVwIGEgbmV3IHVzZXIgd2l0aCBPVFAgdmVyaWZpY2F0aW9uIGFuZCByZWZlcnJhbCBwcm9jZXNzaW5nXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgc2lnblVwKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcsIHVzZXJEYXRhPzogUGFydGlhbDxVc2VyPiAmIHsgcmVmZXJyYWxDb2RlPzogc3RyaW5nIH0pIHtcbiAgICAvLyBDaGVjayBpZiBlbWFpbCB2ZXJpZmljYXRpb24gaXMgcmVxdWlyZWQgKHdpdGggZmFsbGJhY2sgdG8gZmFsc2UgaWYgdGFibGUgZG9lc24ndCBleGlzdClcbiAgICBsZXQgcmVxdWlyZUVtYWlsVmVyaWZpY2F0aW9uID0gZmFsc2VcbiAgICB0cnkge1xuICAgICAgcmVxdWlyZUVtYWlsVmVyaWZpY2F0aW9uID0gYXdhaXQgQWRtaW5TZXR0aW5nc1NlcnZpY2UuZ2V0U2V0dGluZygncmVxdWlyZV9lbWFpbF92ZXJpZmljYXRpb24nKSB8fCBmYWxzZVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAvLyBHcmFjZWZ1bGx5IGhhbmRsZSBhZG1pbiBzZXR0aW5ncyBhY2Nlc3MgZmFpbHVyZXNcbiAgICAgIGNvbnNvbGUud2FybignQ291bGQgbm90IGZldGNoIGVtYWlsIHZlcmlmaWNhdGlvbiBzZXR0aW5nLCBkZWZhdWx0aW5nIHRvIGZhbHNlOicsIGVycm9yKVxuICAgICAgcmVxdWlyZUVtYWlsVmVyaWZpY2F0aW9uID0gZmFsc2VcblxuICAgICAgLy8gSWYgaXQncyBhIDQwNiBlcnJvciBvciBSTFMgcG9saWN5IGVycm9yLCBpdCBtZWFucyB0aGUgdGFibGUgZXhpc3RzIGJ1dCBhY2Nlc3MgaXMgcmVzdHJpY3RlZFxuICAgICAgLy8gVGhpcyBpcyBleHBlY3RlZCBkdXJpbmcgc2lnbnVwIHdoZW4gdXNlciBpcyBub3QgYXV0aGVudGljYXRlZCB5ZXRcbiAgICAgIGlmIChlcnJvciBpbnN0YW5jZW9mIEVycm9yICYmIChlcnJvci5tZXNzYWdlLmluY2x1ZGVzKCc0MDYnKSB8fCBlcnJvci5tZXNzYWdlLmluY2x1ZGVzKCdwb2xpY3knKSkpIHtcbiAgICAgICAgY29uc29sZS5pbmZvKCdBZG1pbiBzZXR0aW5ncyBhY2Nlc3MgcmVzdHJpY3RlZCBkdXJpbmcgc2lnbnVwIC0gdXNpbmcgZGVmYXVsdCB2YWx1ZXMnKVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIFZhbGlkYXRlIHJlZmVycmFsIGNvZGUgaWYgcHJvdmlkZWRcbiAgICBsZXQgcmVmZXJyZXI6IFVzZXIgfCBudWxsID0gbnVsbFxuICAgIGlmICh1c2VyRGF0YT8ucmVmZXJyYWxDb2RlKSB7XG4gICAgICB0cnkge1xuICAgICAgICByZWZlcnJlciA9IGF3YWl0IFJlZmVycmFsU3lzdGVtU2VydmljZS52YWxpZGF0ZVJlZmVycmFsQ29kZSh1c2VyRGF0YS5yZWZlcnJhbENvZGUpXG4gICAgICAgIGlmICghcmVmZXJyZXIpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgcmVmZXJyYWwgY29kZScpXG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUud2FybignQ291bGQgbm90IHZhbGlkYXRlIHJlZmVycmFsIGNvZGU6JywgZXJyb3IpXG4gICAgICAgIC8vIERvbid0IGZhaWwgc2lnbnVwIGZvciByZWZlcnJhbCBjb2RlIGlzc3VlcywganVzdCBsb2cgdGhlIHdhcm5pbmdcbiAgICAgIH1cbiAgICB9XG5cbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLnNpZ25VcCh7XG4gICAgICBlbWFpbCxcbiAgICAgIHBhc3N3b3JkLFxuICAgICAgb3B0aW9uczoge1xuICAgICAgICBkYXRhOiB7XG4gICAgICAgICAgLi4udXNlckRhdGEsXG4gICAgICAgICAgcmVmZXJyYWxfY29kZTogdXNlckRhdGE/LnJlZmVycmFsQ29kZSwgLy8gU3RvcmUgcmVmZXJyYWwgY29kZSBpbiBhdXRoIG1ldGFkYXRhXG4gICAgICAgICAgZnVsbF9uYW1lOiB1c2VyRGF0YT8uZnVsbF9uYW1lLFxuICAgICAgICAgIHBob25lOiB1c2VyRGF0YT8ucGhvbmUsXG4gICAgICAgICAgbG9jYXRpb246IHVzZXJEYXRhPy5sb2NhdGlvbixcbiAgICAgICAgICByZWZlcnJlcl9pZDogcmVmZXJyZXI/LmlkXG4gICAgICAgIH0sXG4gICAgICAgIC8vIERvbid0IHNldCBlbWFpbFJlZGlyZWN0VG8gZm9yIE9UUCBmbG93XG4gICAgICB9XG4gICAgfSlcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yLm1lc3NhZ2UpXG4gICAgfVxuXG4gICAgLy8gSWYgdXNlciBpcyBjcmVhdGVkIGFuZCBlbWFpbCB2ZXJpZmljYXRpb24gaXMgTk9UIHJlcXVpcmVkLCBjcmVhdGUgcHJvZmlsZSBpbW1lZGlhdGVseVxuICAgIGlmIChkYXRhLnVzZXIgJiYgIXJlcXVpcmVFbWFpbFZlcmlmaWNhdGlvbikge1xuICAgICAgdHJ5IHtcbiAgICAgICAgLy8gQ3JlYXRlIGJhc2ljIHByb2ZpbGUgZmlyc3Qgd2l0aG91dCBjb21wbGV4IG9wZXJhdGlvbnNcbiAgICAgICAgYXdhaXQgdGhpcy5jcmVhdGVCYXNpY1VzZXJQcm9maWxlKGRhdGEudXNlciwgdXNlckRhdGEpXG5cbiAgICAgICAgLy8gRGVmZXIgY29tcGxleCBvcGVyYXRpb25zIGxpa2UgcmVmZXJyYWwgaGllcmFyY2h5IHBsYWNlbWVudFxuICAgICAgICBpZiAocmVmZXJyZXIpIHtcbiAgICAgICAgICAvLyBTY2hlZHVsZSByZWZlcnJhbCBwbGFjZW1lbnQgZm9yIGxhdGVyIChkb24ndCBibG9jayBzaWdudXApXG4gICAgICAgICAgdGhpcy5zY2hlZHVsZVJlZmVycmFsUGxhY2VtZW50KGRhdGEudXNlci5pZCwgcmVmZXJyZXIuaWQpLmNhdGNoKGVycm9yID0+IHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBzY2hlZHVsZSByZWZlcnJhbCBwbGFjZW1lbnQ6JywgZXJyb3IpXG4gICAgICAgICAgfSlcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAocHJvZmlsZUVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIHVzZXIgcHJvZmlsZTonLCBwcm9maWxlRXJyb3IpXG4gICAgICAgIC8vIERvbid0IGZhaWwgdGhlIHNpZ251cCwgYnV0IGxvZyB0aGUgZXJyb3JcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4geyBkYXRhLCByZXF1aXJlRW1haWxWZXJpZmljYXRpb24sIHJlZmVycmVyIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBDcmVhdGUgYmFzaWMgdXNlciBwcm9maWxlIHdpdGhvdXQgY29tcGxleCBvcGVyYXRpb25zIChmYXN0KVxuICAgKi9cbiAgcHJpdmF0ZSBzdGF0aWMgYXN5bmMgY3JlYXRlQmFzaWNVc2VyUHJvZmlsZShhdXRoVXNlcjogYW55LCB1c2VyRGF0YT86IFBhcnRpYWw8VXNlcj4gJiB7IHJlZmVycmFsQ29kZT86IHN0cmluZyB9KSB7XG4gICAgLy8gQ2hlY2sgaWYgYWRtaW4gY2xpZW50IGlzIGF2YWlsYWJsZVxuICAgIGlmICghc3VwYWJhc2VBZG1pbikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdBZG1pbiBjbGllbnQgbm90IGF2YWlsYWJsZSAtIG1pc3NpbmcgU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWScpXG4gICAgfVxuXG4gICAgLy8gVXNlIHNlcnZpY2Ugcm9sZSBjbGllbnQgdG8gYnlwYXNzIFJMUyBmb3IgdXNlciBjcmVhdGlvblxuICAgIC8vIENyZWF0ZSBtaW5pbWFsIHByb2ZpbGUgdG8gYXZvaWQgdHJpZ2dlciBkZWxheXNcbiAgICBjb25zdCB7IGVycm9yOiBwcm9maWxlRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlQWRtaW5cbiAgICAgIC5mcm9tKCd1c2VycycpXG4gICAgICAuaW5zZXJ0KHtcbiAgICAgICAgaWQ6IGF1dGhVc2VyLmlkLFxuICAgICAgICBlbWFpbDogYXV0aFVzZXIuZW1haWwhLFxuICAgICAgICBmdWxsX25hbWU6IHVzZXJEYXRhPy5mdWxsX25hbWUsXG4gICAgICAgIHBob25lOiB1c2VyRGF0YT8ucGhvbmUsXG4gICAgICAgIGxvY2F0aW9uOiB1c2VyRGF0YT8ubG9jYXRpb24sXG4gICAgICAgIHVzZXJfdHlwZTogJ3VzZXInLCAvLyBEZWZhdWx0IHVzZXIgdHlwZVxuICAgICAgICByb2xlOiAndXNlcicsIC8vIEVuc3VyZSByb2xlIGlzIHNldFxuICAgICAgICBpc19zdXBlcl9hZG1pbjogZmFsc2VcbiAgICAgICAgLy8gRG9uJ3Qgc2V0IHJlZmVycmFsIGZpZWxkcyBoZXJlIHRvIGF2b2lkIHRyaWdnZXIgY29tcGxleGl0eVxuICAgICAgfSlcblxuICAgIGlmIChwcm9maWxlRXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGNyZWF0ZSB1c2VyIHByb2ZpbGU6ICR7cHJvZmlsZUVycm9yLm1lc3NhZ2V9YClcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogU2NoZWR1bGUgcmVmZXJyYWwgcGxhY2VtZW50IGZvciBsYXRlciBwcm9jZXNzaW5nIChub24tYmxvY2tpbmcpXG4gICAqL1xuICBwcml2YXRlIHN0YXRpYyBhc3luYyBzY2hlZHVsZVJlZmVycmFsUGxhY2VtZW50KHVzZXJJZDogc3RyaW5nLCByZWZlcnJlcklkOiBzdHJpbmcpIHtcbiAgICB0cnkge1xuICAgICAgLy8gVXNlIGEgdGltZW91dCB0byBkZWZlciB0aGlzIG9wZXJhdGlvblxuICAgICAgc2V0VGltZW91dChhc3luYyAoKSA9PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgYXdhaXQgUmVmZXJyYWxTeXN0ZW1TZXJ2aWNlLnBsYWNlVXNlckluSGllcmFyY2h5KHVzZXJJZCwgcmVmZXJyZXJJZClcbiAgICAgICAgICBjb25zb2xlLmxvZygnUmVmZXJyYWwgcGxhY2VtZW50IGNvbXBsZXRlZCBmb3IgdXNlcjonLCB1c2VySWQpXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcignRGVmZXJyZWQgcmVmZXJyYWwgcGxhY2VtZW50IGZhaWxlZDonLCBlcnJvcilcbiAgICAgICAgfVxuICAgICAgfSwgMjAwMCkgLy8gMiBzZWNvbmQgZGVsYXlcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHNjaGVkdWxlIHJlZmVycmFsIHBsYWNlbWVudDonLCBlcnJvcilcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQ3JlYXRlIHVzZXIgcHJvZmlsZSBpbiBwdWJsaWMudXNlcnMgdGFibGUgKGxlZ2FjeSBtZXRob2Qgd2l0aCBmdWxsIG9wZXJhdGlvbnMpXG4gICAqL1xuICBwcml2YXRlIHN0YXRpYyBhc3luYyBjcmVhdGVVc2VyUHJvZmlsZShhdXRoVXNlcjogYW55LCB1c2VyRGF0YT86IFBhcnRpYWw8VXNlcj4gJiB7IHJlZmVycmFsQ29kZT86IHN0cmluZyB9LCByZWZlcnJlcj86IFVzZXIgfCBudWxsKSB7XG4gICAgLy8gQ2hlY2sgaWYgYWRtaW4gY2xpZW50IGlzIGF2YWlsYWJsZVxuICAgIGlmICghc3VwYWJhc2VBZG1pbikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdBZG1pbiBjbGllbnQgbm90IGF2YWlsYWJsZSAtIG1pc3NpbmcgU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWScpXG4gICAgfVxuXG4gICAgLy8gVXNlIHNlcnZpY2Ugcm9sZSBjbGllbnQgdG8gYnlwYXNzIFJMUyBmb3IgdXNlciBjcmVhdGlvblxuICAgIGNvbnN0IHsgZXJyb3I6IHByb2ZpbGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VBZG1pblxuICAgICAgLmZyb20oJ3VzZXJzJylcbiAgICAgIC5pbnNlcnQoe1xuICAgICAgICBpZDogYXV0aFVzZXIuaWQsXG4gICAgICAgIGVtYWlsOiBhdXRoVXNlci5lbWFpbCEsXG4gICAgICAgIGZ1bGxfbmFtZTogdXNlckRhdGE/LmZ1bGxfbmFtZSxcbiAgICAgICAgcGhvbmU6IHVzZXJEYXRhPy5waG9uZSxcbiAgICAgICAgbG9jYXRpb246IHVzZXJEYXRhPy5sb2NhdGlvbixcbiAgICAgICAgdXNlcl90eXBlOiAndXNlcicsIC8vIERlZmF1bHQgdXNlciB0eXBlXG4gICAgICAgIHJlZmVycmFsX2xldmVsOiByZWZlcnJlciA/IChyZWZlcnJlci5yZWZlcnJhbF9sZXZlbCB8fCAwKSArIDEgOiAwLFxuICAgICAgICByZWZlcnJlZF9ieV9pZDogcmVmZXJyZXI/LmlkIHx8IG51bGxcbiAgICAgIH0pXG5cbiAgICBpZiAocHJvZmlsZUVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBjcmVhdGUgdXNlciBwcm9maWxlOiAke3Byb2ZpbGVFcnJvci5tZXNzYWdlfWApXG4gICAgfVxuXG4gICAgLy8gSWYgdXNlciBoYXMgYSByZWZlcnJlciwgcGxhY2UgdGhlbSBpbiBoaWVyYXJjaHlcbiAgICBpZiAocmVmZXJyZXIpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IFJlZmVycmFsU3lzdGVtU2VydmljZS5wbGFjZVVzZXJJbkhpZXJhcmNoeShhdXRoVXNlci5pZCwgcmVmZXJyZXIuaWQpXG4gICAgICB9IGNhdGNoIChyZWZlcnJhbEVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBwbGFjZSB1c2VyIGluIHJlZmVycmFsIGhpZXJhcmNoeTonLCByZWZlcnJhbEVycm9yKVxuICAgICAgICAvLyBEb24ndCBmYWlsIHRoZSBwcm9maWxlIGNyZWF0aW9uLCBidXQgbG9nIHRoZSBlcnJvclxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBWZXJpZnkgZW1haWwgT1RQIGFuZCBwcm9jZXNzIHJlZmVycmFsIHBsYWNlbWVudFxuICAgKi9cbiAgc3RhdGljIGFzeW5jIHZlcmlmeUVtYWlsT3RwKGVtYWlsOiBzdHJpbmcsIHRva2VuOiBzdHJpbmcpIHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLnZlcmlmeU90cCh7XG4gICAgICBlbWFpbCxcbiAgICAgIHRva2VuLFxuICAgICAgdHlwZTogJ3NpZ251cCdcbiAgICB9KVxuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3IubWVzc2FnZSlcbiAgICB9XG5cbiAgICAvLyBBZnRlciBzdWNjZXNzZnVsIHZlcmlmaWNhdGlvbiwgY3JlYXRlIHVzZXIgcHJvZmlsZSBpZiBpdCBkb2Vzbid0IGV4aXN0XG4gICAgaWYgKGRhdGEudXNlcikge1xuICAgICAgdHJ5IHtcbiAgICAgICAgLy8gQ2hlY2sgaWYgdXNlciBwcm9maWxlIGFscmVhZHkgZXhpc3RzXG4gICAgICAgIGNvbnN0IHsgZGF0YTogZXhpc3RpbmdVc2VyIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAgIC5mcm9tKCd1c2VycycpXG4gICAgICAgICAgLnNlbGVjdCgnaWQnKVxuICAgICAgICAgIC5lcSgnaWQnLCBkYXRhLnVzZXIuaWQpXG4gICAgICAgICAgLnNpbmdsZSgpXG5cbiAgICAgICAgaWYgKCFleGlzdGluZ1VzZXIpIHtcbiAgICAgICAgICAvLyBHZXQgcmVmZXJyZXIgaWYgcmVmZXJyYWwgY29kZSBleGlzdHNcbiAgICAgICAgICBsZXQgcmVmZXJyZXI6IFVzZXIgfCBudWxsID0gbnVsbFxuICAgICAgICAgIGlmIChkYXRhLnVzZXIudXNlcl9tZXRhZGF0YT8ucmVmZXJyYWxfY29kZSkge1xuICAgICAgICAgICAgcmVmZXJyZXIgPSBhd2FpdCBSZWZlcnJhbFN5c3RlbVNlcnZpY2UudmFsaWRhdGVSZWZlcnJhbENvZGUoZGF0YS51c2VyLnVzZXJfbWV0YWRhdGEucmVmZXJyYWxfY29kZSlcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAvLyBDcmVhdGUgdXNlciBwcm9maWxlXG4gICAgICAgICAgYXdhaXQgdGhpcy5jcmVhdGVVc2VyUHJvZmlsZShkYXRhLnVzZXIsIHtcbiAgICAgICAgICAgIGZ1bGxfbmFtZTogZGF0YS51c2VyLnVzZXJfbWV0YWRhdGE/LmZ1bGxfbmFtZSxcbiAgICAgICAgICAgIHBob25lOiBkYXRhLnVzZXIudXNlcl9tZXRhZGF0YT8ucGhvbmUsXG4gICAgICAgICAgICBsb2NhdGlvbjogZGF0YS51c2VyLnVzZXJfbWV0YWRhdGE/LmxvY2F0aW9uLFxuICAgICAgICAgICAgcmVmZXJyYWxDb2RlOiBkYXRhLnVzZXIudXNlcl9tZXRhZGF0YT8ucmVmZXJyYWxfY29kZVxuICAgICAgICAgIH0sIHJlZmVycmVyKVxuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChwcm9maWxlRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgdXNlciBwcm9maWxlIGFmdGVyIHZlcmlmaWNhdGlvbjonLCBwcm9maWxlRXJyb3IpXG4gICAgICAgIC8vIERvbid0IGZhaWwgdGhlIHZlcmlmaWNhdGlvbiwgYnV0IGxvZyB0aGUgZXJyb3JcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4gZGF0YVxuICB9XG5cbiAgLyoqXG4gICAqIFJlc2VuZCBlbWFpbCBPVFBcbiAgICovXG4gIHN0YXRpYyBhc3luYyByZXNlbmRFbWFpbE90cChlbWFpbDogc3RyaW5nKSB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5yZXNlbmQoe1xuICAgICAgdHlwZTogJ3NpZ251cCcsXG4gICAgICBlbWFpbFxuICAgIH0pXG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvci5tZXNzYWdlKVxuICAgIH1cblxuICAgIHJldHVybiBkYXRhXG4gIH1cblxuICAvKipcbiAgICogU2lnbiBpbiB3aXRoIGVtYWlsIGFuZCBwYXNzd29yZFxuICAgKi9cbiAgc3RhdGljIGFzeW5jIHNpZ25JbihlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKSB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5zaWduSW5XaXRoUGFzc3dvcmQoe1xuICAgICAgZW1haWwsXG4gICAgICBwYXNzd29yZFxuICAgIH0pXG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvci5tZXNzYWdlKVxuICAgIH1cblxuICAgIHJldHVybiBkYXRhXG4gIH1cblxuICAvKipcbiAgICogU2lnbiBvdXQgdGhlIGN1cnJlbnQgdXNlclxuICAgKi9cbiAgc3RhdGljIGFzeW5jIHNpZ25PdXQoKSB7XG4gICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5zaWduT3V0KClcbiAgICBcbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvci5tZXNzYWdlKVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgdGhlIGN1cnJlbnQgdXNlciBzZXNzaW9uXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZ2V0U2Vzc2lvbigpIHtcbiAgICBjb25zdCB7IGRhdGE6IHsgc2Vzc2lvbiB9LCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRTZXNzaW9uKClcbiAgICBcbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvci5tZXNzYWdlKVxuICAgIH1cblxuICAgIHJldHVybiBzZXNzaW9uXG4gIH1cblxuICAvKipcbiAgICogR2V0IHRoZSBjdXJyZW50IHVzZXIgd2l0aCBpbXByb3ZlZCBlcnJvciBoYW5kbGluZ1xuICAgKi9cbiAgc3RhdGljIGFzeW5jIGdldEN1cnJlbnRVc2VyKCk6IFByb21pc2U8QXV0aFVzZXIgfCBudWxsPiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIEZpcnN0IGNoZWNrIGlmIHdlIGhhdmUgYSB2YWxpZCBzZXNzaW9uXG4gICAgICBjb25zdCB7IGRhdGE6IHsgc2Vzc2lvbiB9LCBlcnJvcjogc2Vzc2lvbkVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFNlc3Npb24oKVxuXG4gICAgICBpZiAoc2Vzc2lvbkVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1Nlc3Npb24gZXJyb3I6Jywgc2Vzc2lvbkVycm9yKVxuICAgICAgICByZXR1cm4gbnVsbFxuICAgICAgfVxuXG4gICAgICBpZiAoIXNlc3Npb24/LnVzZXIpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ05vIGFjdGl2ZSBzZXNzaW9uIGZvdW5kJylcbiAgICAgICAgcmV0dXJuIG51bGxcbiAgICAgIH1cblxuICAgICAgY29uc3QgdXNlciA9IHNlc3Npb24udXNlclxuICAgICAgY29uc29sZS5sb2coJ0ZvdW5kIGFjdGl2ZSBzZXNzaW9uIGZvciB1c2VyOicsIHVzZXIuZW1haWwpXG5cbiAgICAgIC8vIEdldCBhZGRpdGlvbmFsIHVzZXIgZGF0YSBmcm9tIHVzZXJzIHRhYmxlIHdpdGggcmV0cnkgbG9naWNcbiAgICAgIGxldCByZXRyeUNvdW50ID0gMFxuICAgICAgY29uc3QgbWF4UmV0cmllcyA9IDNcblxuICAgICAgd2hpbGUgKHJldHJ5Q291bnQgPCBtYXhSZXRyaWVzKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc3QgeyBkYXRhOiBwcm9maWxlLCBlcnJvcjogcHJvZmlsZUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAgICAgLmZyb20oJ3VzZXJzJylcbiAgICAgICAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgICAgICAgLmVxKCdpZCcsIHVzZXIuaWQpXG4gICAgICAgICAgICAuc2luZ2xlKClcblxuICAgICAgICAgIGlmIChwcm9maWxlRXJyb3IpIHtcbiAgICAgICAgICAgIGlmIChwcm9maWxlRXJyb3IuY29kZSA9PT0gJ1BHUlNUMTE2Jykge1xuICAgICAgICAgICAgICAvLyBVc2VyIHByb2ZpbGUgZG9lc24ndCBleGlzdCwgY3JlYXRlIGl0XG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdVc2VyIHByb2ZpbGUgbm90IGZvdW5kLCBjcmVhdGluZy4uLicpXG4gICAgICAgICAgICAgIGNvbnN0IG5ld1Byb2ZpbGUgPSB7XG4gICAgICAgICAgICAgICAgaWQ6IHVzZXIuaWQsXG4gICAgICAgICAgICAgICAgZW1haWw6IHVzZXIuZW1haWwhLFxuICAgICAgICAgICAgICAgIGZ1bGxfbmFtZTogdXNlci51c2VyX21ldGFkYXRhPy5mdWxsX25hbWUgfHwgbnVsbCxcbiAgICAgICAgICAgICAgICBwaG9uZTogdXNlci51c2VyX21ldGFkYXRhPy5waG9uZSB8fCBudWxsLFxuICAgICAgICAgICAgICAgIGxvY2F0aW9uOiB1c2VyLnVzZXJfbWV0YWRhdGE/LmxvY2F0aW9uIHx8IG51bGwsXG4gICAgICAgICAgICAgICAgcm9sZTogJ3VzZXInLFxuICAgICAgICAgICAgICAgIGlzX3N1cGVyX2FkbWluOiBmYWxzZVxuICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgY29uc3QgeyBkYXRhOiBjcmVhdGVkUHJvZmlsZSwgZXJyb3I6IGNyZWF0ZUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAgICAgICAgIC5mcm9tKCd1c2VycycpXG4gICAgICAgICAgICAgICAgLmluc2VydChuZXdQcm9maWxlKVxuICAgICAgICAgICAgICAgIC5zZWxlY3QoKVxuICAgICAgICAgICAgICAgIC5zaW5nbGUoKVxuXG4gICAgICAgICAgICAgIGlmIChjcmVhdGVFcnJvcikge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIHVzZXIgcHJvZmlsZTonLCBjcmVhdGVFcnJvcilcbiAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgaWQ6IHVzZXIuaWQsXG4gICAgICAgICAgICAgICAgICBlbWFpbDogdXNlci5lbWFpbCFcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICByZXR1cm4gY3JlYXRlZFByb2ZpbGVcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgIHRocm93IHByb2ZpbGVFcnJvclxuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cblxuICAgICAgICAgIHJldHVybiBwcm9maWxlXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgcmV0cnlDb3VudCsrXG4gICAgICAgICAgY29uc29sZS5lcnJvcihgRXJyb3IgZmV0Y2hpbmcgdXNlciBwcm9maWxlIChhdHRlbXB0ICR7cmV0cnlDb3VudH0pOmAsIGVycm9yKVxuXG4gICAgICAgICAgaWYgKHJldHJ5Q291bnQgPj0gbWF4UmV0cmllcykge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcignTWF4IHJldHJpZXMgcmVhY2hlZCwgcmV0dXJuaW5nIGJhc2ljIHVzZXIgaW5mbycpXG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICBpZDogdXNlci5pZCxcbiAgICAgICAgICAgICAgZW1haWw6IHVzZXIuZW1haWwhXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gV2FpdCBiZWZvcmUgcmV0cnlcbiAgICAgICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwMCAqIHJldHJ5Q291bnQpKVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIHJldHVybiBudWxsXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluIGdldEN1cnJlbnRVc2VyOicsIGVycm9yKVxuICAgICAgcmV0dXJuIG51bGxcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogVXBkYXRlIHVzZXIgcHJvZmlsZVxuICAgKi9cbiAgc3RhdGljIGFzeW5jIHVwZGF0ZVByb2ZpbGUodXNlcklkOiBzdHJpbmcsIHVwZGF0ZXM6IFBhcnRpYWw8VXNlcj4pIHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3VzZXJzJylcbiAgICAgIC51cGRhdGUodXBkYXRlcylcbiAgICAgIC5lcSgnaWQnLCB1c2VySWQpXG4gICAgICAuc2VsZWN0KClcbiAgICAgIC5zaW5nbGUoKVxuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3IubWVzc2FnZSlcbiAgICB9XG5cbiAgICByZXR1cm4gZGF0YVxuICB9XG5cbiAgLyoqXG4gICAqIFJlc2V0IHBhc3N3b3JkXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgcmVzZXRQYXNzd29yZChlbWFpbDogc3RyaW5nKSB7XG4gICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5yZXNldFBhc3N3b3JkRm9yRW1haWwoZW1haWwsIHtcbiAgICAgIHJlZGlyZWN0VG86IGAke3dpbmRvdy5sb2NhdGlvbi5vcmlnaW59L2F1dGgvcmVzZXQtcGFzc3dvcmRgXG4gICAgfSlcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yLm1lc3NhZ2UpXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFVwZGF0ZSBwYXNzd29yZFxuICAgKi9cbiAgc3RhdGljIGFzeW5jIHVwZGF0ZVBhc3N3b3JkKG5ld1Bhc3N3b3JkOiBzdHJpbmcpIHtcbiAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLnVwZGF0ZVVzZXIoe1xuICAgICAgcGFzc3dvcmQ6IG5ld1Bhc3N3b3JkXG4gICAgfSlcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yLm1lc3NhZ2UpXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIExpc3RlbiB0byBhdXRoIHN0YXRlIGNoYW5nZXMgd2l0aCBpbXByb3ZlZCBoYW5kbGluZ1xuICAgKi9cbiAgc3RhdGljIG9uQXV0aFN0YXRlQ2hhbmdlKGNhbGxiYWNrOiAoZXZlbnQ6IHN0cmluZywgc2Vzc2lvbjogYW55KSA9PiB2b2lkKSB7XG4gICAgcmV0dXJuIHN1cGFiYXNlLmF1dGgub25BdXRoU3RhdGVDaGFuZ2UoYXN5bmMgKGV2ZW50LCBzZXNzaW9uKSA9PiB7XG4gICAgICBjb25zb2xlLmxvZygnQXV0aCBzdGF0ZSBjaGFuZ2UgZGV0ZWN0ZWQ6JywgZXZlbnQsIHNlc3Npb24/LnVzZXI/LmVtYWlsKVxuXG4gICAgICAvLyBBZGQgYSBzbWFsbCBkZWxheSB0byBlbnN1cmUgc3RhdGUgY29uc2lzdGVuY3lcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBjYWxsYmFjayhldmVudCwgc2Vzc2lvbilcbiAgICAgIH0sIDEwMClcbiAgICB9KVxuICB9XG5cbiAgLyoqXG4gICAqIENoZWNrIGlmIGN1cnJlbnQgc2Vzc2lvbiBpcyB2YWxpZFxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGlzU2Vzc2lvblZhbGlkKCk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGRhdGE6IHsgc2Vzc2lvbiB9LCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRTZXNzaW9uKClcblxuICAgICAgaWYgKGVycm9yIHx8ICFzZXNzaW9uKSB7XG4gICAgICAgIHJldHVybiBmYWxzZVxuICAgICAgfVxuXG4gICAgICAvLyBDaGVjayBpZiB0b2tlbiBpcyBleHBpcmVkXG4gICAgICBjb25zdCBub3cgPSBNYXRoLmZsb29yKERhdGUubm93KCkgLyAxMDAwKVxuICAgICAgaWYgKHNlc3Npb24uZXhwaXJlc19hdCAmJiBzZXNzaW9uLmV4cGlyZXNfYXQgPCBub3cpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ1Nlc3Npb24gdG9rZW4gZXhwaXJlZCcpXG4gICAgICAgIHJldHVybiBmYWxzZVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gdHJ1ZVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjaGVja2luZyBzZXNzaW9uIHZhbGlkaXR5OicsIGVycm9yKVxuICAgICAgcmV0dXJuIGZhbHNlXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFJlZnJlc2ggc2Vzc2lvbiBpZiBuZWVkZWRcbiAgICovXG4gIHN0YXRpYyBhc3luYyByZWZyZXNoU2Vzc2lvbigpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5yZWZyZXNoU2Vzc2lvbigpXG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciByZWZyZXNoaW5nIHNlc3Npb246JywgZXJyb3IpXG4gICAgICAgIHJldHVybiBmYWxzZVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gISFkYXRhLnNlc3Npb25cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgaW4gcmVmcmVzaFNlc3Npb246JywgZXJyb3IpXG4gICAgICByZXR1cm4gZmFsc2VcbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJzdXBhYmFzZSIsInN1cGFiYXNlQWRtaW4iLCJBZG1pblNldHRpbmdzU2VydmljZSIsIlJlZmVycmFsU3lzdGVtU2VydmljZSIsIkF1dGhTZXJ2aWNlIiwic2lnblVwIiwiZW1haWwiLCJwYXNzd29yZCIsInVzZXJEYXRhIiwicmVxdWlyZUVtYWlsVmVyaWZpY2F0aW9uIiwiZ2V0U2V0dGluZyIsImVycm9yIiwiY29uc29sZSIsIndhcm4iLCJFcnJvciIsIm1lc3NhZ2UiLCJpbmNsdWRlcyIsImluZm8iLCJyZWZlcnJlciIsInJlZmVycmFsQ29kZSIsInZhbGlkYXRlUmVmZXJyYWxDb2RlIiwiZGF0YSIsImF1dGgiLCJvcHRpb25zIiwicmVmZXJyYWxfY29kZSIsImZ1bGxfbmFtZSIsInBob25lIiwibG9jYXRpb24iLCJyZWZlcnJlcl9pZCIsImlkIiwidXNlciIsImNyZWF0ZUJhc2ljVXNlclByb2ZpbGUiLCJzY2hlZHVsZVJlZmVycmFsUGxhY2VtZW50IiwiY2F0Y2giLCJwcm9maWxlRXJyb3IiLCJhdXRoVXNlciIsImZyb20iLCJpbnNlcnQiLCJ1c2VyX3R5cGUiLCJyb2xlIiwiaXNfc3VwZXJfYWRtaW4iLCJ1c2VySWQiLCJyZWZlcnJlcklkIiwic2V0VGltZW91dCIsInBsYWNlVXNlckluSGllcmFyY2h5IiwibG9nIiwiY3JlYXRlVXNlclByb2ZpbGUiLCJyZWZlcnJhbF9sZXZlbCIsInJlZmVycmVkX2J5X2lkIiwicmVmZXJyYWxFcnJvciIsInZlcmlmeUVtYWlsT3RwIiwidG9rZW4iLCJ2ZXJpZnlPdHAiLCJ0eXBlIiwiZXhpc3RpbmdVc2VyIiwic2VsZWN0IiwiZXEiLCJzaW5nbGUiLCJ1c2VyX21ldGFkYXRhIiwicmVzZW5kRW1haWxPdHAiLCJyZXNlbmQiLCJzaWduSW4iLCJzaWduSW5XaXRoUGFzc3dvcmQiLCJzaWduT3V0IiwiZ2V0U2Vzc2lvbiIsInNlc3Npb24iLCJnZXRDdXJyZW50VXNlciIsInNlc3Npb25FcnJvciIsInJldHJ5Q291bnQiLCJtYXhSZXRyaWVzIiwicHJvZmlsZSIsImNvZGUiLCJuZXdQcm9maWxlIiwiY3JlYXRlZFByb2ZpbGUiLCJjcmVhdGVFcnJvciIsIlByb21pc2UiLCJyZXNvbHZlIiwidXBkYXRlUHJvZmlsZSIsInVwZGF0ZXMiLCJ1cGRhdGUiLCJyZXNldFBhc3N3b3JkIiwicmVzZXRQYXNzd29yZEZvckVtYWlsIiwicmVkaXJlY3RUbyIsIndpbmRvdyIsIm9yaWdpbiIsInVwZGF0ZVBhc3N3b3JkIiwibmV3UGFzc3dvcmQiLCJ1cGRhdGVVc2VyIiwib25BdXRoU3RhdGVDaGFuZ2UiLCJjYWxsYmFjayIsImV2ZW50IiwiaXNTZXNzaW9uVmFsaWQiLCJub3ciLCJNYXRoIiwiZmxvb3IiLCJEYXRlIiwiZXhwaXJlc19hdCIsInJlZnJlc2hTZXNzaW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/auth.ts\n"));

/***/ })

});