-- Fix RLS policies to allow authenticated users to create their own profiles
-- This fixes the "Admin client not available" error during signup

-- Drop existing problematic policies
DROP POLICY IF EXISTS "Users can insert own profile" ON users;
DROP POLICY IF EXISTS "Admins can insert users" ON users;
DROP POLICY IF EXISTS "Authenticated users can insert own profile" ON users;
DROP POLICY IF EXISTS "Users can insert profile during signup" ON users;

-- Create the main policy that allows authenticated users to create their own profile
CREATE POLICY "Users can insert profile during signup" ON users
    FOR INSERT
    WITH CHECK (auth.uid() = id);

-- Ensure users can read basic info (needed for referral validation)
-- Only recreate if it doesn't exist
DROP POLICY IF EXISTS "Users can view basic user info" ON users;
DROP POLICY IF EXISTS "Public can view basic user info" ON users;
CREATE POLICY "Public can view basic user info" ON users
    FOR SELECT
    USING (true);

-- Allow users to update their own profiles
DROP POLICY IF EXISTS "Users can update own profile" ON users;
CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE
    USING (auth.uid() = id OR is_admin());

-- Allow admins to manage users (drop existing first)
DROP POLICY IF EXISTS "Admins can manage users" ON users;
CREATE POLICY "Admins can manage users" ON users
    FOR ALL
    USING (is_admin())
    WITH CHECK (is_admin());

-- Add comment for clarity
COMMENT ON POLICY "Users can insert profile during signup" ON users IS 'Allows authenticated users to create their own profile during signup process';
COMMENT ON POLICY "Public can view basic user info" ON users IS 'Allows public read access to basic user information for ads and referrals';
COMMENT ON POLICY "Users can update own profile" ON users IS 'Allows users to update their own profile or admins to update any profile';
COMMENT ON POLICY "Admins can manage users" ON users IS 'Allows admins full access to manage all user profiles';
