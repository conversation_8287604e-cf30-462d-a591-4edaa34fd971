// <PERSON><PERSON><PERSON> to fix admin_settings RLS policies via Supabase client
// Run this with: node fix-admin-settings-rls.js

const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config()

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function fixAdminSettingsRLS() {
  console.log('Fixing admin_settings RLS policies...')
  
  try {
    // Drop existing restrictive policies
    const dropPolicies = [
      'DROP POLICY IF EXISTS "Admin users can read admin settings" ON admin_settings',
      'DROP POLICY IF EXISTS "admin_settings_select_policy" ON admin_settings',
      'DROP POLICY IF EXISTS "Admin users can insert admin settings" ON admin_settings',
      'DROP POLICY IF EXISTS "admin_settings_insert_policy" ON admin_settings',
      'DROP POLICY IF EXISTS "Admin users can update admin settings" ON admin_settings',
      'DROP POLICY IF EXISTS "admin_settings_update_policy" ON admin_settings',
      'DROP POLICY IF EXISTS "Admin users can delete admin settings" ON admin_settings',
      'DROP POLICY IF EXISTS "admin_settings_delete_policy" ON admin_settings',
      'DROP POLICY IF EXISTS "Admins can view admin settings" ON admin_settings',
      'DROP POLICY IF EXISTS "Admins can insert admin settings" ON admin_settings',
      'DROP POLICY IF EXISTS "Admins can update admin settings" ON admin_settings'
    ]
    
    for (const sql of dropPolicies) {
      console.log('Executing:', sql)
      const { error } = await supabase.rpc('exec_sql', { sql })
      if (error) {
        console.warn('Warning dropping policy:', error.message)
      }
    }
    
    // Create new policies that allow public read access
    const createPolicies = [
      // Allow anyone to read admin settings (needed for signup process)
      `CREATE POLICY "Public can read admin settings" ON admin_settings FOR SELECT USING (true)`,
      
      // Only admins can insert new settings
      `CREATE POLICY "Admin users can insert admin settings" ON admin_settings
       FOR INSERT WITH CHECK (
         EXISTS (
           SELECT 1 FROM users 
           WHERE users.id = auth.uid() 
           AND (users.role = 'admin' OR users.is_super_admin = true)
         )
       )`,
      
      // Only admins can update existing settings
      `CREATE POLICY "Admin users can update admin settings" ON admin_settings
       FOR UPDATE USING (
         EXISTS (
           SELECT 1 FROM users 
           WHERE users.id = auth.uid() 
           AND (users.role = 'admin' OR users.is_super_admin = true)
         )
       )`,
      
      // Only admins can delete settings
      `CREATE POLICY "Admin users can delete admin settings" ON admin_settings
       FOR DELETE USING (
         EXISTS (
           SELECT 1 FROM users 
           WHERE users.id = auth.uid() 
           AND (users.role = 'admin' OR users.is_super_admin = true)
         )
       )`
    ]
    
    for (const sql of createPolicies) {
      console.log('Executing:', sql)
      const { error } = await supabase.rpc('exec_sql', { sql })
      if (error) {
        console.error('Error creating policy:', error.message)
      } else {
        console.log('✓ Policy created successfully')
      }
    }
    
    // Ensure essential settings exist
    const settings = [
      { key: 'require_email_verification', value: false, description: 'Whether email verification is required for new users' },
      { key: 'allow_registration', value: true, description: 'Whether new user registration is allowed' },
      { key: 'site_name', value: 'OKDOI', description: 'Name of the website' },
      { key: 'free_ads_limit', value: 2, description: 'Number of free ads new users can post without a subscription' }
    ]
    
    for (const setting of settings) {
      const { error } = await supabase
        .from('admin_settings')
        .upsert({
          key: setting.key,
          value: setting.value,
          description: setting.description,
          updated_at: new Date().toISOString()
        })
      
      if (error) {
        console.error(`Error upserting setting ${setting.key}:`, error.message)
      } else {
        console.log(`✓ Setting ${setting.key} ensured`)
      }
    }
    
    console.log('✅ Admin settings RLS policies fixed successfully!')
    
  } catch (error) {
    console.error('Error fixing admin settings RLS:', error)
    process.exit(1)
  }
}

// Create exec_sql function if it doesn't exist
async function createExecSqlFunction() {
  const sql = `
    CREATE OR REPLACE FUNCTION exec_sql(sql text)
    RETURNS void AS $$
    BEGIN
      EXECUTE sql;
    END;
    $$ LANGUAGE plpgsql SECURITY DEFINER;
  `
  
  const { error } = await supabase.rpc('exec_sql', { sql })
  if (error && !error.message.includes('already exists')) {
    // Try direct query if RPC doesn't work
    console.log('Creating exec_sql function...')
  }
}

async function main() {
  await createExecSqlFunction()
  await fixAdminSettingsRLS()
}

main().catch(console.error)
