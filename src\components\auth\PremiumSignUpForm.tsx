'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Eye, EyeOff, UserPlus, CheckCircle, AlertCircle, Gift } from 'lucide-react'
import PremiumButton from '@/components/ui/PremiumButton'
import FloatingInput from '@/components/ui/FloatingInput'
import GlassCard, { GlassCardHeader, GlassCardTitle, GlassCardContent } from '@/components/ui/GlassCard'
import { AuthLogo } from '@/components/ui/Logo'
import { useAuth } from '@/contexts/AuthContext'
import { AdminSettingsService } from '@/lib/services/adminSettings'

interface PremiumSignUpFormProps {
  redirectTo?: string
  referralCode?: string
}

export default function PremiumSignUpForm({ redirectTo = '/', referralCode }: PremiumSignUpFormProps) {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    fullName: '',
    phone: '',
    location: '',
    referralCode: referralCode || ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [acceptTerms, setAcceptTerms] = useState(false)
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState('')
  const [requireEmailVerification, setRequireEmailVerification] = useState(false)

  const { signUp } = useAuth()
  const router = useRouter()

  useEffect(() => {
    const checkEmailVerificationSetting = async () => {
      try {
        const setting = await AdminSettingsService.getSetting('require_email_verification')
        setRequireEmailVerification(setting || false)
      } catch (error) {
        // Gracefully handle admin settings access failures during signup
        console.warn('Could not fetch email verification setting, defaulting to false:', error)
        setRequireEmailVerification(false)

        // If it's a 406 error or RLS policy error, it's expected during signup
        if (error instanceof Error && (error.message.includes('406') || error.message.includes('policy'))) {
          console.info('Admin settings access restricted during signup - using default values')
        }
      }
    }
    checkEmailVerificationSetting()
  }, [])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  const validateForm = () => {
    if (!formData.fullName.trim()) {
      setError('Full name is required')
      return false
    }
    if (!formData.email) {
      setError('Email is required')
      return false
    }
    if (!/\S+@\S+\.\S+/.test(formData.email)) {
      setError('Please enter a valid email address')
      return false
    }
    if (!formData.password) {
      setError('Password is required')
      return false
    }
    if (formData.password.length < 6) {
      setError('Password must be at least 6 characters long')
      return false
    }
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match')
      return false
    }
    if (!acceptTerms) {
      setError('Please accept the Terms of Service and Privacy Policy')
      return false
    }
    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setSuccess('')

    if (!validateForm()) return

    setLoading(true)

    try {
      const result = await signUp(formData.email, formData.password, {
        full_name: formData.fullName,
        phone: formData.phone,
        location: formData.location,
        referral_code: formData.referralCode
      })

      if (result.requireEmailVerification) {
        // Redirect to OTP verification page
        router.push(`/auth/verify-otp?email=${encodeURIComponent(formData.email)}`)
      } else {
        setSuccess('Account created successfully! Welcome to OKDOI!')
        setTimeout(() => {
          router.push(redirectTo)
        }, 1500)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during registration')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="w-full max-w-md mx-auto">
      <GlassCard variant="elevated" padding="lg">
        <GlassCardHeader>
          <div className="text-center mb-2">
            <div className="flex justify-center mb-4">
              <AuthLogo />
            </div>
            <GlassCardTitle>Join OKDOI</GlassCardTitle>
            <p className="text-gray-600 mt-2">Create your premium marketplace account</p>
            
            {referralCode && (
              <div className="mt-4 p-3 bg-gradient-to-r from-accent-orange/10 to-accent-red/10 rounded-xl border border-accent-orange/20">
                <div className="flex items-center justify-center text-accent-orange">
                  <Gift className="h-4 w-4 mr-2" />
                  <span className="text-sm font-medium">You're invited! Special benefits await.</span>
                </div>
              </div>
            )}
          </div>
        </GlassCardHeader>

        <GlassCardContent>
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl mb-6 flex items-center animate-in slide-in-from-top-2 duration-300">
              <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          {success && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-xl mb-6 flex items-center animate-in slide-in-from-top-2 duration-300">
              <CheckCircle className="h-5 w-5 mr-2 flex-shrink-0" />
              <span className="text-sm">{success}</span>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-5">
            <FloatingInput
              label="Full Name"
              name="fullName"
              value={formData.fullName}
              onChange={handleChange}
              fullWidth
              disabled={loading}
            />

            <FloatingInput
              label="Email Address"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              fullWidth
              disabled={loading}
            />

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <FloatingInput
                label="Phone (Optional)"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={handleChange}
                fullWidth
                disabled={loading}
              />

              <FloatingInput
                label="Location (Optional)"
                name="location"
                value={formData.location}
                onChange={handleChange}
                fullWidth
                disabled={loading}
              />
            </div>

            {!referralCode && (
              <FloatingInput
                label="Referral Code (Optional)"
                name="referralCode"
                value={formData.referralCode}
                onChange={handleChange}
                fullWidth
                disabled={loading}
                helperText="Enter a friend's referral code to get special benefits"
              />
            )}

            <div className="relative">
              <FloatingInput
                label="Password"
                name="password"
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={handleChange}
                fullWidth
                disabled={loading}
                helperText="Must be at least 6 characters long"
              />
              <button
                type="button"
                className="absolute right-4 top-4 text-gray-400 hover:text-gray-600 transition-colors"
                onClick={() => setShowPassword(!showPassword)}
                disabled={loading}
              >
                {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
              </button>
            </div>

            <div className="relative">
              <FloatingInput
                label="Confirm Password"
                name="confirmPassword"
                type={showConfirmPassword ? 'text' : 'password'}
                value={formData.confirmPassword}
                onChange={handleChange}
                fullWidth
                disabled={loading}
              />
              <button
                type="button"
                className="absolute right-4 top-4 text-gray-400 hover:text-gray-600 transition-colors"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                disabled={loading}
              >
                {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
              </button>
            </div>

            <div className="flex items-start">
              <input
                type="checkbox"
                checked={acceptTerms}
                onChange={(e) => setAcceptTerms(e.target.checked)}
                className="rounded border-gray-300 text-primary-blue focus:ring-primary-blue focus:ring-offset-0 mt-1 transition-colors"
                disabled={loading}
              />
              <span className="ml-3 text-sm text-gray-600">
                I agree to the{' '}
                <Link href="/terms" className="text-primary-blue hover:text-primary-blue/80 font-medium">
                  Terms of Service
                </Link>{' '}
                and{' '}
                <Link href="/privacy" className="text-primary-blue hover:text-primary-blue/80 font-medium">
                  Privacy Policy
                </Link>
              </span>
            </div>

            <PremiumButton
              type="submit"
              variant="gradient"
              size="lg"
              loading={loading}
              fullWidth
              icon={<UserPlus className="h-5 w-5" />}
            >
              {loading ? 'Creating Account...' : 'Create Account'}
            </PremiumButton>
          </form>

          <div className="mt-8 text-center">
            <p className="text-gray-600">
              Already have an account?{' '}
              <Link
                href="/auth/signin"
                className="text-primary-blue hover:text-primary-blue/80 font-semibold transition-colors"
              >
                Sign In
              </Link>
            </p>
          </div>
        </GlassCardContent>
      </GlassCard>
    </div>
  )
}
