// Test script to verify optimized signup performance
const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseAnonKey || !supabaseServiceKey) {
  console.error('Missing required environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey)

async function testOptimizedSignup() {
  console.log('🧪 Testing optimized signup performance...\n')
  
  const testEmail = `testuser${Date.now()}@gmail.com`
  const testPassword = 'testpassword123'
  
  try {
    console.log('1. Testing signup speed...')
    const startTime = Date.now()
    
    const { data, error } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          full_name: 'Test User',
          phone: '1234567890',
          location: 'Test Location'
        }
      }
    })
    
    const signupTime = Date.now() - startTime
    console.log(`⏱️  Signup completed in ${signupTime}ms`)
    
    if (error) {
      console.error('❌ Signup error:', {
        message: error.message,
        status: error.status,
        code: error.code
      })
      return false
    }
    
    if (!data.user) {
      console.error('❌ No user data returned')
      return false
    }
    
    console.log('✅ User created successfully:', {
      id: data.user.id,
      email: data.user.email,
      confirmed: data.user.email_confirmed_at ? 'Yes' : 'No'
    })
    
    // Test 2: Check if user profile was created
    console.log('\n2. Checking user profile creation...')
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', data.user.id)
      .single()
    
    if (profileError) {
      console.error('❌ Profile check error:', profileError.message)
      return false
    }
    
    console.log('✅ User profile created:', {
      id: profile.id,
      email: profile.email,
      full_name: profile.full_name,
      referral_code: profile.referral_code ? 'Generated' : 'Missing'
    })
    
    // Test 3: Check wallet creation (on-demand)
    console.log('\n3. Testing on-demand wallet creation...')
    const walletStartTime = Date.now()
    
    const { data: walletId, error: walletError } = await supabaseAdmin
      .rpc('ensure_user_wallet', { p_user_id: data.user.id })
    
    const walletTime = Date.now() - walletStartTime
    console.log(`⏱️  Wallet creation completed in ${walletTime}ms`)
    
    if (walletError) {
      console.error('❌ Wallet creation error:', walletError.message)
      return false
    }
    
    console.log('✅ Wallet created on-demand:', walletId)
    
    // Test 4: Performance benchmark
    console.log('\n4. Performance Summary:')
    console.log(`- Signup Time: ${signupTime}ms ${signupTime < 5000 ? '✅' : '❌ (>5s)'}`)
    console.log(`- Wallet Creation: ${walletTime}ms ${walletTime < 1000 ? '✅' : '❌ (>1s)'}`)
    console.log(`- Total Time: ${signupTime + walletTime}ms`)
    
    // Cleanup - delete test user
    console.log('\n5. Cleaning up test user...')
    const { error: deleteError } = await supabaseAdmin.auth.admin.deleteUser(data.user.id)
    
    if (deleteError) {
      console.warn('⚠️  Failed to delete test user:', deleteError.message)
    } else {
      console.log('✅ Test user cleaned up')
    }
    
    return signupTime < 10000 // Success if signup takes less than 10 seconds
    
  } catch (error) {
    console.error('❌ Exception during signup test:', error.message)
    return false
  }
}

async function testBatchSignup() {
  console.log('\n🧪 Testing batch signup performance...\n')
  
  const batchSize = 5
  const signupPromises = []
  const startTime = Date.now()
  
  try {
    for (let i = 1; i <= batchSize; i++) {
      const testEmail = `batchtest${Date.now()}_${i}@gmail.com`
      const testPassword = 'testpassword123'
      
      signupPromises.push(
        supabase.auth.signUp({
          email: testEmail,
          password: testPassword,
          options: {
            data: {
              full_name: `Batch Test User ${i}`,
              phone: `123456789${i}`,
              location: 'Test Location'
            }
          }
        })
      )
    }
    
    const results = await Promise.allSettled(signupPromises)
    const totalTime = Date.now() - startTime
    
    const successful = results.filter(r => r.status === 'fulfilled' && !r.value.error).length
    const failed = results.length - successful
    
    console.log('📊 Batch Signup Results:')
    console.log(`- Total Users: ${batchSize}`)
    console.log(`- Successful: ${successful} ✅`)
    console.log(`- Failed: ${failed} ${failed > 0 ? '❌' : '✅'}`)
    console.log(`- Total Time: ${totalTime}ms`)
    console.log(`- Average Time: ${Math.round(totalTime / batchSize)}ms per user`)
    
    // Cleanup batch test users
    console.log('\n🧹 Cleaning up batch test users...')
    for (const result of results) {
      if (result.status === 'fulfilled' && result.value.data?.user) {
        try {
          await supabaseAdmin.auth.admin.deleteUser(result.value.data.user.id)
        } catch (error) {
          console.warn('Failed to delete batch user:', error.message)
        }
      }
    }
    
    return successful === batchSize && totalTime < 30000 // Success if all users created in <30s
    
  } catch (error) {
    console.error('❌ Exception during batch signup test:', error.message)
    return false
  }
}

async function main() {
  console.log('🚀 Testing OKDOI Optimized Signup Performance...\n')
  
  const singleTest = await testOptimizedSignup()
  const batchTest = await testBatchSignup()
  
  console.log('\n📋 Final Results:')
  console.log('- Single Signup:', singleTest ? '✅ PASSED' : '❌ FAILED')
  console.log('- Batch Signup:', batchTest ? '✅ PASSED' : '❌ FAILED')
  
  if (singleTest && batchTest) {
    console.log('\n🎉 All tests passed! Signup performance is optimized.')
  } else {
    console.log('\n❌ Some tests failed. Check the errors above.')
  }
}

main().catch(console.error)
