"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/signup/page",{

/***/ "(app-pages-browser)/./src/lib/services/adminSettings.ts":
/*!*******************************************!*\
  !*** ./src/lib/services/adminSettings.ts ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminSettingsService: function() { return /* binding */ AdminSettingsService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n\nclass AdminSettingsService {\n    /**\n   * Get all admin settings with fallback to defaults\n   */ static async getSettings() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"admin_settings\").select(\"key, value\");\n            if (error) {\n                // Handle RLS policy errors gracefully\n                if (error.code === \"PGRST301\" || error.message.includes(\"policy\")) {\n                    console.info(\"RLS policy blocked access to admin settings, using defaults\");\n                } else {\n                    console.warn(\"Failed to fetch settings, using defaults:\", error.message, error.code);\n                }\n                return this.getDefaultSettings();\n            }\n            // Convert array of key-value pairs to object\n            const settingsMap = {};\n            data === null || data === void 0 ? void 0 : data.forEach((setting)=>{\n                settingsMap[setting.key] = setting.value;\n            });\n            var _settingsMap_allow_registration, _settingsMap_require_email_verification, _settingsMap_auto_approve_ads, _settingsMap_max_images_per_ad, _settingsMap_ad_expiry_days, _settingsMap_enable_notifications, _settingsMap_maintenance_mode, _settingsMap_enable_analytics, _settingsMap_enable_referrals, _settingsMap_free_ads_limit;\n            // Return settings with proper types and defaults\n            return {\n                siteName: settingsMap.site_name || \"OKDOI\",\n                siteDescription: settingsMap.site_description || \"Premium Marketplace for Everything\",\n                adminEmail: settingsMap.admin_email || \"<EMAIL>\",\n                allowRegistration: (_settingsMap_allow_registration = settingsMap.allow_registration) !== null && _settingsMap_allow_registration !== void 0 ? _settingsMap_allow_registration : true,\n                requireEmailVerification: (_settingsMap_require_email_verification = settingsMap.require_email_verification) !== null && _settingsMap_require_email_verification !== void 0 ? _settingsMap_require_email_verification : false,\n                autoApproveAds: (_settingsMap_auto_approve_ads = settingsMap.auto_approve_ads) !== null && _settingsMap_auto_approve_ads !== void 0 ? _settingsMap_auto_approve_ads : false,\n                maxImagesPerAd: (_settingsMap_max_images_per_ad = settingsMap.max_images_per_ad) !== null && _settingsMap_max_images_per_ad !== void 0 ? _settingsMap_max_images_per_ad : 10,\n                adExpiryDays: (_settingsMap_ad_expiry_days = settingsMap.ad_expiry_days) !== null && _settingsMap_ad_expiry_days !== void 0 ? _settingsMap_ad_expiry_days : 30,\n                enableNotifications: (_settingsMap_enable_notifications = settingsMap.enable_notifications) !== null && _settingsMap_enable_notifications !== void 0 ? _settingsMap_enable_notifications : true,\n                maintenanceMode: (_settingsMap_maintenance_mode = settingsMap.maintenance_mode) !== null && _settingsMap_maintenance_mode !== void 0 ? _settingsMap_maintenance_mode : false,\n                enableAnalytics: (_settingsMap_enable_analytics = settingsMap.enable_analytics) !== null && _settingsMap_enable_analytics !== void 0 ? _settingsMap_enable_analytics : true,\n                enableReferrals: (_settingsMap_enable_referrals = settingsMap.enable_referrals) !== null && _settingsMap_enable_referrals !== void 0 ? _settingsMap_enable_referrals : true,\n                freeAdsLimit: (_settingsMap_free_ads_limit = settingsMap.free_ads_limit) !== null && _settingsMap_free_ads_limit !== void 0 ? _settingsMap_free_ads_limit : 2\n            };\n        } catch (error) {\n            // Handle network errors, 406 responses, etc.\n            console.warn(\"Error fetching settings, using defaults:\", error);\n            // If it's a fetch error with 406 status, it's likely an RLS policy issue\n            if (error instanceof Error && error.message.includes(\"406\")) {\n                console.info(\"HTTP 406 error fetching settings, likely RLS policy restriction - using defaults\");\n            }\n            return this.getDefaultSettings();\n        }\n    }\n    /**\n   * Get default settings when table is not available\n   */ static getDefaultSettings() {\n        return {\n            siteName: \"OKDOI\",\n            siteDescription: \"Premium Marketplace for Everything\",\n            adminEmail: \"<EMAIL>\",\n            allowRegistration: true,\n            requireEmailVerification: false,\n            autoApproveAds: false,\n            maxImagesPerAd: 10,\n            adExpiryDays: 30,\n            enableNotifications: true,\n            maintenanceMode: false,\n            enableAnalytics: true,\n            enableReferrals: true,\n            freeAdsLimit: 2\n        };\n    }\n    /**\n   * Update admin settings\n   */ static async updateSettings(settings) {\n        const updates = [];\n        // Convert settings object to database format\n        if (settings.siteName !== undefined) {\n            updates.push({\n                key: \"site_name\",\n                value: settings.siteName\n            });\n        }\n        if (settings.siteDescription !== undefined) {\n            updates.push({\n                key: \"site_description\",\n                value: settings.siteDescription\n            });\n        }\n        if (settings.adminEmail !== undefined) {\n            updates.push({\n                key: \"admin_email\",\n                value: settings.adminEmail\n            });\n        }\n        if (settings.allowRegistration !== undefined) {\n            updates.push({\n                key: \"allow_registration\",\n                value: settings.allowRegistration\n            });\n        }\n        if (settings.requireEmailVerification !== undefined) {\n            updates.push({\n                key: \"require_email_verification\",\n                value: settings.requireEmailVerification\n            });\n        }\n        if (settings.autoApproveAds !== undefined) {\n            updates.push({\n                key: \"auto_approve_ads\",\n                value: settings.autoApproveAds\n            });\n        }\n        if (settings.maxImagesPerAd !== undefined) {\n            updates.push({\n                key: \"max_images_per_ad\",\n                value: settings.maxImagesPerAd\n            });\n        }\n        if (settings.adExpiryDays !== undefined) {\n            updates.push({\n                key: \"ad_expiry_days\",\n                value: settings.adExpiryDays\n            });\n        }\n        if (settings.enableNotifications !== undefined) {\n            updates.push({\n                key: \"enable_notifications\",\n                value: settings.enableNotifications\n            });\n        }\n        if (settings.maintenanceMode !== undefined) {\n            updates.push({\n                key: \"maintenance_mode\",\n                value: settings.maintenanceMode\n            });\n        }\n        if (settings.enableAnalytics !== undefined) {\n            updates.push({\n                key: \"enable_analytics\",\n                value: settings.enableAnalytics\n            });\n        }\n        if (settings.enableReferrals !== undefined) {\n            updates.push({\n                key: \"enable_referrals\",\n                value: settings.enableReferrals\n            });\n        }\n        if (settings.freeAdsLimit !== undefined) {\n            updates.push({\n                key: \"free_ads_limit\",\n                value: settings.freeAdsLimit\n            });\n        }\n        // Update each setting\n        for (const update of updates){\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"admin_settings\").upsert({\n                key: update.key,\n                value: update.value,\n                updated_at: new Date().toISOString()\n            });\n            if (error) {\n                throw new Error(\"Failed to update setting \".concat(update.key, \": \").concat(error.message));\n            }\n        }\n    }\n    /**\n   * Get a specific setting value with fallback defaults\n   */ static async getSetting(key) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"admin_settings\").select(\"value\").eq(\"key\", key).single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    return this.getDefaultSetting(key) // Setting not found, return default\n                    ;\n                }\n                // Handle RLS policy errors (common during signup when user is not authenticated)\n                if (error.code === \"PGRST301\" || error.message.includes(\"policy\")) {\n                    console.info(\"RLS policy blocked access to setting \".concat(key, \", using default value\"));\n                    return this.getDefaultSetting(key);\n                }\n                // If table doesn't exist or other error, return default\n                console.warn(\"Error fetching setting \".concat(key, \":\"), error.message, error.code);\n                return this.getDefaultSetting(key);\n            }\n            return data === null || data === void 0 ? void 0 : data.value;\n        } catch (error) {\n            // Handle network errors, 406 responses, etc.\n            console.warn(\"Error fetching setting \".concat(key, \":\"), error);\n            // If it's a fetch error with 406 status, it's likely an RLS policy issue\n            if (error instanceof Error && error.message.includes(\"406\")) {\n                console.info(\"HTTP 406 error for setting \".concat(key, \", likely RLS policy restriction - using default\"));\n            }\n            return this.getDefaultSetting(key);\n        }\n    }\n    /**\n   * Get default value for a setting\n   */ static getDefaultSetting(key) {\n        const defaults = {\n            \"site_name\": \"OKDOI\",\n            \"site_description\": \"Premium Marketplace for Everything\",\n            \"admin_email\": \"<EMAIL>\",\n            \"allow_registration\": true,\n            \"require_email_verification\": false,\n            \"auto_approve_ads\": false,\n            \"max_images_per_ad\": 10,\n            \"ad_expiry_days\": 30,\n            \"enable_notifications\": true,\n            \"maintenance_mode\": false,\n            \"enable_analytics\": true,\n            \"enable_referrals\": true,\n            \"free_ads_limit\": 2\n        };\n        return defaults[key] || null;\n    }\n    /**\n   * Update a specific setting\n   */ static async updateSetting(key, value) {\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"admin_settings\").upsert({\n            key,\n            value,\n            updated_at: new Date().toISOString()\n        });\n        if (error) {\n            throw new Error(\"Failed to update setting \".concat(key, \": \").concat(error.message));\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/adminSettings.ts\n"));

/***/ })

});